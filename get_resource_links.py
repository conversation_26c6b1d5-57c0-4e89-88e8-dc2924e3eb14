import core.resource_core as tsttresource_core
from core.resource_client import TSTTResourceClient

if __name__ == "__main__":
    client = TSTTResourceClient()
    
    # Manually construct the search criteria
    body = {
        "title": "blank",
        "componentId": 1118899,
        "rowLimit": 100,
        "sorting": [{'columnName': 'id', 'columnOrder': 1}]
    }

    try:
        client.verify_token()
        response = client._invoke_core_request_function(tsttresource_core.find_resources, body, 60)
        
        if response and 'resources' in response:
            for resource in response['resources']:
                if 'id' in resource:
                    print(f"rdar://res/{resource['id']}")
                else:
                    print("Found resource without an ID.")
        else:
            print("No resources found or response format is incorrect.")
            print("Response:", response)

    except Exception as e:
        print(f"An error occurred: {e}")