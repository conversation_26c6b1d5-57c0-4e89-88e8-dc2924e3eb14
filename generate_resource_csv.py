import csv
import json
import sys
from core.resource_client import TSTTResourceClient

def generate_resource_csv(input_csv_path, id_column_name, output_csv_path="output_resources.csv"):
    """
    Reads resource definition IDs from an input CSV, fetches resource details using TSTTResourceClient,
    and generates a new CSV with resource definition ID, resource title, and resource component name.
    """
    resource_client = TSTTResourceClient()
    # Dictionary to store resource components grouped by resdef_id
    # Dictionary to store component versions for each resdef_id
    resdef_component_versions = {}
    # Set to store all unique component versions found across all resdef_ids
    all_unique_component_versions = set()

    try:
        with open(input_csv_path, mode='r', newline='', encoding='utf-8') as infile:
            reader = csv.DictReader(infile)
            if id_column_name not in reader.fieldnames:
                print(f"Error: Column '{id_column_name}' not found in '{input_csv_path}'")
                return

            for row in reader:
                resdef_id_str = row[id_column_name]
                if not resdef_id_str:
                    continue

                try:
                    resdef_id = int(resdef_id_str)
                    
                    if resdef_id not in resdef_component_versions:
                        resdef_component_versions[resdef_id] = set()

                    response = resource_client.resource_definition_for_id(resdef_id)
                    
                    if response:
                        if 'resources' in response and response['resources']:
                            for resource in response['resources']:
                                component_data = resource.get('component')
                                if component_data and isinstance(component_data, dict):
                                    component_version = component_data.get('version', 'N/A')
                                    resdef_component_versions[resdef_id].add(component_version)
                                    all_unique_component_versions.add(component_version)
                        else:
                            component_data = response.get('component')
                            if component_data and isinstance(component_data, dict):
                                component_version = component_data.get('version', 'N/A')
                                resdef_component_versions[resdef_id].add(component_version)
                                all_unique_component_versions.add(component_version)
                    else:
                        print(f"Warning: No response for resource definition ID {resdef_id}")
                        resdef_component_versions[resdef_id].add("N/A")
                        all_unique_component_versions.add("N/A")

                except ValueError:
                    print(f"Warning: Invalid resourcedefinitionID '{resdef_id_str}' in row: {row}. Skipping this entry.")
                except Exception as e:
                    print(f"Error processing resourcedefinitionID {resdef_id_str}: {e}. Skipping this entry.")

    except FileNotFoundError:
        print(f"Error: Input CSV file not found at '{input_csv_path}'")
        return
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return

    # Prepare output data
    output_data = []
    sorted_unique_component_versions = sorted(list(all_unique_component_versions))
    output_header = ["resourcedefinitionID"] + sorted_unique_component_versions
    output_data.append(output_header)

    for resdef_id in sorted(resdef_component_versions.keys()):
        row = [resdef_id]
        for version in sorted_unique_component_versions:
            row.append(True if version in resdef_component_versions[resdef_id] else False)
        output_data.append(row)

    try:
        with open(output_csv_path, mode='w', newline='', encoding='utf-8') as outfile:
            writer = csv.writer(outfile)
            writer.writerows(output_data)
        print(f"Successfully generated output CSV: '{output_csv_path}'")
    except Exception as e:
        print(f"Error writing output CSV file: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python generate_resource_csv.py <input_csv_path> <id_column_name>")
        sys.exit(1)

    input_file = sys.argv[1]
    column_name = sys.argv[2]
    generate_resource_csv(input_file, column_name)