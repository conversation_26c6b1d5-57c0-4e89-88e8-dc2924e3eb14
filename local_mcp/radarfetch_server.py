#!/usr/bin/env python3.11
"""
RadarFetch MCP Server scaffold (Step 1 & 2 & 3)

- Step 1: Determines auth strategy from environment variables:
  * OAuth if RADAR_OIDC_CLIENT_ID and RADAR_OIDC_CLIENT_SECRET are set
  * Otherwise fall back to AppleConnect
- Step 2: Creates a global TSTTResourceClient instance (radarclient)
- Step 3: Creates FastMCP("RadarFetch") server instance when available
"""
from __future__ import annotations

import os
import sys
import site
import re
from typing import Literal, Optional

# Ensure site-packages take precedence over the project root to avoid local 'mcp' shadowing
try:
    user_site = site.getusersitepackages()
    if isinstance(user_site, str) and user_site not in sys.path:
        sys.path.insert(0, user_site)
    try:
        for p in site.getsitepackages():
            if p not in sys.path:
                sys.path.insert(0, p)
    except Exception:
        pass
except Exception:
    pass

# FastMCP 2.0 import (upgraded from official MCP SDK)
try:
    from fastmcp import FastMCP  # FastMCP 2.0 (standalone package)
    _FASTMCP_SOURCE = "fastmcp_2.0"
except Exception:
    # Fallback to official MCP SDK's FastMCP 1.0
    try:
        from mcp.server.fastmcp import FastMCP  # official SDK import
        _FASTMCP_SOURCE = "official_1.0"
    except Exception:
        # Do not fail at scaffold stage; FastMCP used in Step 3
        FastMCP = None  # type: ignore
        _FASTMCP_SOURCE = "missing"

# Core client used across the app
# Add the parent directory to the sys.path to allow for absolute imports
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
try:
    from core.resource_client import TSTTResourceClient
except Exception as e:
    # Keep explicit import error to help diagnosis during early testing
    raise ImportError("Failed to import TSTTResourceClient: {}".format(e))

AuthStrategy = Literal["oauth", "appleconnect"]


def get_auth_strategy() -> AuthStrategy:
    """Return the authentication strategy based on environment variables.

    - If RADAR_OIDC_CLIENT_ID and RADAR_OIDC_CLIENT_SECRET are present, use "oauth".
    - Otherwise, fall back to "appleconnect".
    """
    client_id = os.getenv("RADAR_OIDC_CLIENT_ID")
    client_secret = os.getenv("RADAR_OIDC_CLIENT_SECRET")
    if client_id and client_secret:
        return "oauth"
    return "appleconnect"


# Step 1 result: computed once at import
AUTH_STRATEGY: AuthStrategy = get_auth_strategy()

# Step 2: global client instance for all tool functions
radarclient: TSTTResourceClient = TSTTResourceClient()

# Step 3: create MCP server instance (if FastMCP available)
MCP_SERVER_NAME = "RadarFetch"
mcp: Optional[FastMCP] = None
if FastMCP is not None:
    try:
        mcp = FastMCP(MCP_SERVER_NAME)
    except Exception:
        # Keep module import-safe even if FastMCP fails to initialize
        mcp = None


def _health_summary() -> str:
    """Return a short, human-readable health summary for smoke checks."""
    parts = [
        "RadarFetch MCP scaffold loaded",
        "auth_strategy={}".format(AUTH_STRATEGY),
        "fastmcp_source={}".format(_FASTMCP_SOURCE),
        "mcp_instance={}".format("created" if mcp is not None else "not_created"),
        "client_ready={}".format("yes" if isinstance(radarclient, TSTTResourceClient) else "no"),
    ]
    return "; ".join(parts)


__all__ = [
    "get_auth_strategy",
    "AUTH_STRATEGY",
    "radarclient",
    "mcp",
    "MCP_SERVER_NAME",
    "_health_summary",
]


# Helper functions for title processing and resdef extraction

def _clean_chipset(title: str) -> str:
    """Remove brackets and curly braces from title."""
    title = re.sub(r"\[.*\]", '', title)  # Remove []
    title = re.sub(r"\{.*\}", '', title)  # Remove {}
    return title

def _clean_store(title: str) -> str:
    """Remove store information from title."""
    title = re.sub(r"\(Store.*?\)", '', title)  # Remove Store
    return title

def _clean_prefix(title: str) -> str:
    """Remove Shanghai prefix from title."""
    title = re.sub(r"\(Shanghai.*?\)", '', title)  # Remove Shanghai
    return title

def _clean_title(title: str) -> tuple[str, str]:
    """Clean title and extract speed_resdef part.

    Returns:
        tuple: (cleaned_title, speed_resdef)
    """
    # Remove brackets, curly braces, store info, leading/trailing spaces
    title = _clean_chipset(title)
    title = _clean_store(title)
    title = _clean_prefix(title)
    title = title.lstrip()  # Remove leading spaces

    if title.rfind(' - ') == -1:
        new_title = title
        speed_resdef = ""
    else:
        new_title = title[:title.rfind(' - ')]
        speed_resdef = title[title.rfind(' - ')+3:]

    return (new_title, speed_resdef)

def _extract_resdef(speed_resdef: str) -> str:
    """Extract 5-digit resdef ID from speed_resdef string."""
    try:
        match = re.search(r'\d{5}', speed_resdef)
        if match:
            return match.group()
    except Exception:
        pass
    return ""

def _extract_title_priority(resdef_id: int) -> tuple[str, str]:
    """Extract priority and title from resource definition keywords.

    Returns:
        tuple: (priority, title)
    """
    try:
        resource_data = radarclient.fields_for_resource_definition_id(resdef_id, request_fields=['keywords', 'title'])
        keywords = [keyword['keyword']['name'] for keyword in resource_data["keywords"]]
        title = resource_data['title']

        # Pattern to match 'ATC USB' followed by digits and 'P' followed by digits
        pattern_usb = r'ATC USB(\d+) P(\d+)'
        # Pattern to match 'ATC TBT' followed by digits and 'P' followed by digits
        pattern_tbt = r'ATC CIO P(\d+)'

        priority = None
        for keyword in keywords:
            match_usb = re.search(pattern_usb, keyword)
            match_tbt = re.search(pattern_tbt, keyword)
            if match_usb:
                priority = match_usb.group(2)
                break
            elif match_tbt:
                priority = match_tbt.group(1)
                break

        return (priority or "", title or "")
    except Exception:
        return ("", "")

def _attach_resource_to_resdef_internal(resource_id: int, resdef_id: int) -> tuple[bool, str]:
    """Internal helper function to attach a resource to a resource definition.

    Returns:
        tuple: (success: bool, message: str)
    """
    try:
        # Build attachment payload (matching Python res2resdef.py lines 34-43)
        attach_payload = {
            "resources": {
                "upsert": [
                    {
                        "id": resource_id,
                        "include": True
                    }
                ]
            }
        }

        # Attach resource to resource definition
        radarclient.update_resource_definition(resdef_id, attach_payload)
        return (True, f"Resource {resource_id} attached to resdef {resdef_id}")

    except Exception as e:
        # Handle "already attached" case like Python script (res2resdef.py lines 49-50)
        if 'already attached to the resource' in str(e):
            return (True, f"Resource {resource_id} already attached to resdef {resdef_id}")
        else:
            return (False, f"Failed to attach resource {resource_id} to resdef {resdef_id}: {e}")

# --- Step 4: Define Resource tools (CRUD essentials) ---

def _register_tool(func):
    """Decorator shim to optionally register a function as an MCP tool.

    If FastMCP is available and an MCP instance exists, returns the decorated
    function via @mcp.tool(). Otherwise, returns the function unchanged so it can
    still be used directly and unit-tested without FastMCP installed.
    """
    if mcp is not None:
        return mcp.tool()(func)  # type: ignore[misc]
    return func


@_register_tool
def get_resource_by_id(radar_id: int) -> str:
    """Get a resource by its ID from the Radar test suite.

    This function retrieves detailed information about a specific resource using the
    GET /tests/resources/{resource-id} endpoint.

    Parameters:
        radar_id: The resource ID to retrieve (positive integer).
                 Example: 286044, 298608

    Returns:
        A string representation of the resource including:
        - Resource ID and title
        - Component information
        - Priority level
        - Location details
        - Creation/modification dates
        - Associated resource definition

    Example Usage:
        get_resource_by_id(286044)
        # Returns: "Resource 286044: title='USB Hub Device (resdef: 27776)'"

    API Endpoint: GET /tests/resources/{resource-id}
    Required Permissions: Basic resource read access
    """
    try:
        data = radarclient.resource_for_id(radar_id)
        # Try common shapes
        title = None
        if isinstance(data, dict):
            title = data.get("title") or data.get("resource", {}).get("title")
        title = title or "(no title)"
        return f"Resource {radar_id}: title='{title}'"
    except Exception as e:
        return f"Error: failed to fetch resource {radar_id}: {e}"


def _extract_created_id(resp) -> int:
    """Best-effort extraction of created resource id from various response shapes."""
    try:
        if hasattr(resp, "json"):
            j = resp.json()
            if isinstance(j, dict) and "id" in j:
                return int(j["id"])  # type: ignore[arg-type]
        if isinstance(resp, dict) and "id" in resp:
            return int(resp["id"])  # type: ignore[arg-type]
    except Exception:
        pass
    raise ValueError("Cannot extract created resource id from response")


@_register_tool
def create_resource(
    title: str = "",
    resdef_id: int = 0,
    priority: int = -1,
    quantity: int = 1,
    location: str = "",
) -> str:
    """Create one or more Radar resources using the POST /tests/resources endpoint.

    This function creates resources following the Python script logic, automatically
    fetching resource definitions and keywords for complete resource creation.

    Parameters:
        title: Title for the resource(s). If empty, will use resource definition title.
               Example: "USB Hub Device", "MacBook Pro 16-inch"
        resdef_id: Resource definition ID (positive integer). If 0, will extract from title.
                  Example: 27776, 28440
        priority: Priority value 1-5. If -1, will get from resource definition keywords.
                 1=Highest, 2=High, 3=Medium, 4=Low, 5=Lowest
        quantity: Number of resources to create (default 1, max typically 250).
        location: Optional specific location string.
                 Example: "Lab A", "Building 1 Room 101"

    Required Fields (auto-populated):
        - componentId: Retrieved from resource definition (default: 1118899 TSTT)
        - classId: Retrieved from resource definition (default: 1 Hardware)
        - categoryId: Retrieved from resource definition
        - title: From parameter or resource definition
        - priority: From parameter or keywords API

    Returns:
        Success: "Created resource: rdar://res/298608" (single)
                "Created 3 resources: rdar://res/298608, rdar://res/298610, rdar://res/298612" (multiple)
        Error: Detailed error message with validation failures

    Example Usage:
        create_resource(title="Test Device", resdef_id=27776, quantity=1)
        create_resource(resdef_id=27776, quantity=3, location="Test Lab")

    API Endpoint: POST /tests/resources
    Required Permissions: Resource creation access
    Schema: CreateResourceRequest (componentId, classId, categoryId, title, priority required)
    """
    try:
        if quantity <= 0:
            return "Error: quantity must be >= 1"

        # Validate input: at least one of title or resdef_id must be provided
        if not title and not resdef_id:
            return "Error: at least one of title or resdef_id must be provided"

        # Step 1: Determine final title and resdef_id following Python script logic
        final_title = title
        final_resdef_id = resdef_id

        if resdef_id:
            # If resdef_id is provided, get title and priority from resource definition
            try:
                priority_from_resdef, title_from_resdef = _extract_title_priority(resdef_id)
                if title_from_resdef:
                    if not title:  # Only use resdef title if user didn't provide one
                        final_title = title_from_resdef
                    # Always append resdef info to title (matching Python script line 276)
                    final_title = final_title + f" (resdef: {resdef_id})"
                # Use priority from resdef if user didn't specify one
                if priority == -1 and priority_from_resdef:
                    priority = int(priority_from_resdef)
            except Exception as e:
                return f"Error: failed to get resource definition data for {resdef_id}: {e}"
        elif title:
            # If only title is provided, extract resdef_id from title
            cleaned_title, speed_resdef = _clean_title(title)
            extracted_resdef = _extract_resdef(speed_resdef)
            if extracted_resdef:
                final_resdef_id = int(extracted_resdef)
                final_title = cleaned_title  # Use cleaned title
            else:
                return "Error: could not extract resdef_id from title, please provide resdef_id"

        # Step 2: Set default priority if still not determined
        if priority == -1:
            priority = 5  # DEFAULT_PRIORITY

        # Step 3: Get category from resource definition (required for Method 1 payload)
        try:
            resdef_data = radarclient.resource_definition_for_id(final_resdef_id)
            if isinstance(resdef_data, dict) and "category" in resdef_data:
                category_id = resdef_data["category"]["id"]
            else:
                return f"Error: could not get category for resdef {final_resdef_id}"
        except Exception as e:
            return f"Error: failed to get resource definition {final_resdef_id}: {e}"

        # Step 4: Create resources
        created_ids = []
        for _ in range(quantity):
            # Build payload matching Python script Method 1 (Resource.py lines 52-63)
            # This is the primary create_resource method that includes categoryId
            payload = {
                "title": final_title.strip(),
                "componentId": 1118899,         # DEFAULT_COMPONENT_ID
                "classId": 1,                   # DEFAULT_CLASS_ID
                "stateId": 1,                   # DEFAULT_STATE_ID
                "categoryId": category_id,      # Retrieved from resource definition
                "priority": priority + 1,       # API expects priority + 1 (incremented)
                "locationId": 66842,            # DEFAULT_LOCATION_ID
                "driId": 973776146,             # DEFAULT_DRI_ID
                "inventoryKeeperId": 973776146, # DEFAULT_INVENTORY_KEEPER_ID
            }

            # Add specificLocation if provided
            if location:
                payload["specificLocation"] = location
            else:
                payload["specificLocation"] = "Aruba"  # DEFAULT_SPECIFIC_LOCATION

            # Create the resource
            resp = radarclient.create_resource(payload)
            rid = _extract_created_id(resp)
            created_ids.append(rid)

            # Attach resource to resource definition using the internal helper
            success, message = _attach_resource_to_resdef_internal(rid, final_resdef_id)
            if not success:
                return f"Created resource {rid} but {message.lower()}"

        # Return individual rdar:// links like Python/Swift implementations
        if len(created_ids) == 1:
            return f"Created resource: rdar://res/{created_ids[0]}"
        else:
            individual_links = [f"rdar://res/{rid}" for rid in created_ids]
            combined_link = f"rdar://res/{','.join(str(i) for i in created_ids)}"
            return f"Created {len(created_ids)} resources:\nCombined: {combined_link}\nIndividual: {', '.join(individual_links)}"
    except Exception as e:
        return f"Error: failed to create resource(s): {e}"


# @_register_tool  # DISABLED: Delete APIs not accessible - requires special permissions
def delete_resource(resource_id: int) -> str:
    """Delete a resource by ID via DELETE /tests/resources/{resource-id}.

    This function permanently removes a resource from the Radar test suite.
    Use with caution as this operation cannot be undone.

    Parameters:
        resource_id: The resource ID to delete (positive integer).
                    Example: 286044, 298608

    Security Considerations:
        - Resource deletion may be restricted by system policies
        - Some resources may have dependencies that prevent deletion
        - Production systems often protect against accidental deletion
        - May require special permissions or admin access

    Returns:
        Success: "Deleted resource: ID 298608 (Test Device)"
        Error: Permission denied, resource not found, or dependency conflicts

    Example Usage:
        delete_resource(298608)
        # Permanently removes resource 298608

    API Endpoint: DELETE /tests/resources/{resource-id}
    Required Permissions: Resource deletion access (may be restricted)
    Expected Response: HTTP 204 No Content on success
    Note: This operation is irreversible - use with caution
    """
    try:
        if resource_id <= 0:
            return "Error: resource_id must be positive"

        # Get resource title for confirmation message
        try:
            resource_data = radarclient.resource_for_id(resource_id)
            if isinstance(resource_data, dict):
                title = resource_data.get("title") or resource_data.get("resource", {}).get("title") or f"Resource {resource_id}"
            else:
                title = f"Resource {resource_id}"
        except Exception:
            title = f"Resource {resource_id}"

        # Delete the resource using direct radar_request call
        # Import here to avoid circular imports
        import core.resource_core as tsttresource_core

        # Call radar_request directly with the token from the client
        response = tsttresource_core.radar_request(
            f'/tests/resources/{resource_id}',
            'DELETE',
            {},  # headers
            None,  # body
            token=radarclient.radar_token.token,
            timeout=60
        )

        # Check if deletion was successful (HTTP 204 No Content expected for DELETE)
        if hasattr(response, 'status_code') and response.status_code == 204:
            return f"Deleted resource: rdar://res/{resource_id} ({title})"
        else:
            return f"Error: unexpected response when deleting resource {resource_id}: status={getattr(response, 'status_code', 'unknown')}"
    except Exception as e:
        return f"Error: failed to delete resource {resource_id}: {e}"





@_register_tool
def find_resources(
    title: str = "",
    component_id: int = 0,
    priority: int = -1,
    max_results: int = 1000
) -> str:
    """Find resources using advanced search criteria via POST /tests/resources/find.

    This function performs complex resource searches using the FindResourcesRequest schema,
    supporting multiple search criteria and filters.

    Parameters:
        title: Title substring to search for (case-insensitive partial match).
               Example: "USB", "MacBook", "Test Device"
        component_id: Component ID to filter by (positive integer).
                     Example: 1118899 (TSTT), 1234567 (other components)
        priority: Priority level to filter by (1-5, -1 means no filter).
                 1=Highest, 2=High, 3=Medium, 4=Low, 5=Lowest
        max_results: Maximum number of results to return (1-1000, default 100).

    Search Capabilities:
        - Title substring matching (case-insensitive)
        - Component-based filtering
        - Priority-based filtering
        - Pagination support
        - Multiple criteria combination

    Returns:
        Success: "Found X resources:\n1. Resource 286044: USB Hub Device\n2. Resource 298608: Test Device..."
        No results: "No resources found matching the criteria"
        Error: Detailed error message

    API Restrictions:
        - Title-only searches are not allowed (too broad)
        - Must combine title with component_id or priority
        - Component-only or priority-only searches are allowed

    Example Usage:
        find_resources(component_id=1118899, max_results=10)  # ✅ Allowed
        find_resources(title="blank", component_id=1118899)   # ✅ Allowed
        find_resources(title="USB", priority=3)              # ✅ Allowed
        find_resources(title="blank")                         # ❌ Not allowed

    API Endpoint: POST /tests/resources/find
    Required Permissions: Resource search access
    Schema: FindResourcesRequest (supports title, componentId, priority, pagination)
    Response: FindResourcesResponse with resource array
    """
    try:
        if max_results <= 0:
            max_results = 100  # Default to 100 instead of 20
        if max_results > 500:
            max_results = 1000  # Increase reasonable limit to 500

        # Build search criteria using the correct API format from radarAPIdoc.json
        search_criteria = {}

        if title:
            # Use StringField format with 'like' for substring search
            search_criteria["title"] = {"like": f"%{title}%"}

        if component_id > 0:
            # Use ComponentField format with IdentityField for id
            search_criteria["component"] = {"id": {"eq": component_id}}

        if priority >= 0:
            # Use IdentityField format with 'eq'
            search_criteria["priority"] = {"eq": priority}

        if not search_criteria:
            return "Error: at least one search criterion must be provided"

        # API restriction: title-only searches are not allowed for broad terms
        # Must combine title with other criteria like component_id or priority
        if len(search_criteria) == 1 and "title" in search_criteria:
            return "Error: title-only searches are restricted by the API. Please combine title with component_id or priority to narrow the search."

        # Use the existing find_resources function from resource_core
        import core.resource_core as tsttresource_core

        # The find_resources function expects just the search criteria, not a complex body
        # Call the find_resources function with the correct required fields
        # Based on user specification, these are the required fields for find resources
        supported_fields = [
            'assignee', 'assigneeLastModifiedAt', 'category', 'class', 'component',
            'createdAt', 'dri', 'inventoryKeeper', 'isActive', 'lastModifiedAt',
            'location', 'originator', 'priority', 'resourceId', 'specificLocation',
            'state', 'title'
        ]
        results = tsttresource_core.find_resources(
            search_criteria,  # Search criteria in correct API format
            timeout=60,
            token=radarclient.radar_token.token,
            request_fields=supported_fields
        )

        if not results:
            return "No resources found matching the criteria"

        # Format results with proper resource ID extraction
        lines = []
        for r in results[:max_results]:
            title = r.get("title", "No title")
            priority = r.get("priority", "N/A")
            resource_id = r.get("resourceId", "unknown")
            state = r.get("state", "N/A")
            location = r.get("specificLocation", r.get("location", "N/A"))

            # Format with resource ID and additional useful info
            lines.append(f"ID: {resource_id} - {title} (Priority: {priority}, State: {state}, Location: {location})")

        return f"Found {len(lines)} resources (showing {len(lines)} of {len(results)} total):\n" + "\n".join(lines)

    except Exception as e:
        return f"Error: failed to find resources: {e}"


@_register_tool
def get_resource_categories() -> str:
    """Get available resource categories via GET /tests/resources/attributes/categories.

    This function retrieves all available resource categories organized by class,
    essential for resource creation and validation.

    Parameters:
        None required.

    Returns:
        A formatted list containing:
        - Category ID (for API calls)
        - Category name (human-readable)
        - Class information (Hardware, Software, Media, Accounts)
        - Total count of categories

    Category Classes:
        - Class 1: Hardware (physical devices, components)
        - Class 2: Software (applications, licenses)
        - Class 3: Media (content, documentation)
        - Class 4: Accounts (user accounts, credentials)

    Example Categories:
        - 25: Adapter (Class 1 - Hardware)
        - 56: Flash Drive (Class 1 - Hardware)
        - 12: Application (Class 2 - Software)
        - 89: Video Content (Class 3 - Media)

    Returns:
        Success: "Available resource categories (98):\n25 - Adapter (Class 1)\n56 - Flash Drive (Class 1)..."
        Error: Detailed error message

    Example Usage:
        get_resource_categories()
        # Use returned category IDs in create_resource_definition(category_id=25)

    API Endpoint: GET /tests/resources/attributes/categories
    Required Permissions: Basic resource attribute read access
    Response Format: {"categories": [{"id": 25, "value": "Adapter", "class": 1}, ...]}
    """
    try:
        # Use direct radar_request call for GET /tests/resources/attributes/categories
        import core.resource_core as tsttresource_core

        response = tsttresource_core.radar_request(
            '/tests/resources/attributes/categories',
            'GET',
            {},  # headers
            None,  # body
            token=radarclient.radar_token.token,
            timeout=60
        )

        # Check if request was successful
        if hasattr(response, 'status_code') and response.status_code == 200:
            # Parse the response - let's see what we actually get
            if hasattr(response, 'json') and callable(response.json):
                try:
                    categories = response.json()
                except:
                    categories = response.text if hasattr(response, 'text') else str(response)
            elif hasattr(response, 'text'):
                try:
                    import json
                    categories = json.loads(response.text)
                except:
                    categories = response.text
            else:
                categories = str(response)

            # Debug: show the raw response format
            debug_info = f"Raw response type: {type(categories)}, content: {categories}"

            # Handle different response formats
            if isinstance(categories, dict) and "categories" in categories:
                # API returns {"categories": [...]} format
                category_list = categories["categories"]
                lines = []
                for category in category_list:
                    if isinstance(category, dict):
                        cat_id = category.get("id", "unknown")
                        cat_value = category.get("value", category.get("name", "No name"))
                        class_id = category.get("classId", "")
                        if class_id:
                            lines.append(f"{cat_id} - {cat_value} (Class: {class_id})")
                        else:
                            lines.append(f"{cat_id} - {cat_value}")
                    else:
                        lines.append(str(category))
                return f"Available resource categories ({len(lines)}):\n" + "\n".join(lines)
            elif isinstance(categories, list):
                # Direct list of category objects
                lines = []
                for category in categories:
                    if isinstance(category, dict):
                        cat_id = category.get("id", "unknown")
                        cat_value = category.get("value", category.get("name", "No name"))
                        class_id = category.get("classId", "")
                        if class_id:
                            lines.append(f"{cat_id} - {cat_value} (Class: {class_id})")
                        else:
                            lines.append(f"{cat_id} - {cat_value}")
                    else:
                        lines.append(str(category))
                return f"Available resource categories ({len(lines)}):\n" + "\n".join(lines)
            else:
                # Unexpected format
                return f"Resource categories response: {categories}\n\nDebug: {debug_info}"

        else:
            return f"Error: API returned status {getattr(response, 'status_code', 'unknown')}"

    except Exception as e:
        return f"Error: failed to get resource categories: {e}"


@_register_tool
def create_resource_definition(
    title: str,
    category_id: int,
    component_id: int = 1118899,
    class_id: int = 1,
    priority: int = 5,
    discussion: str = ""
) -> str:
    """Create a new resource definition via POST /tests/resourcedefinitions.

    This function creates resource definitions that serve as templates for creating
    actual resources. Resource definitions define the type, category, and properties
    of resources that can be created.

    Parameters:
        title: Title of the resource definition (required, 1-240 characters).
               Example: "USB Hub Device", "MacBook Pro 16-inch M1", "Test Equipment"
        category_id: Category ID from get_resource_categories() (required, positive integer).
                    Example: 25 (Adapter), 56 (Flash Drive), 12 (Application)
        component_id: Component ID (default 1118899 - TSTT, positive integer).
                     Identifies the owning component/team
        class_id: Class ID (default 1 for hardware, positive integer).
                 1=Hardware, 2=Software, 3=Media, 4=Accounts
        priority: Priority level 1-5 (default 5).
                 1=Highest, 2=High, 3=Medium, 4=Low, 5=Lowest
        discussion: Discussion/description text (optional).
                   Example: "Standard USB hub for testing connectivity"

    Required Fields (API Schema):
        - title: String (1-240 chars)
        - categoryId: Integer (from categories API)
        - classId: Integer (1-4)
        - componentId: Integer (team/component ID)

    Returns:
        Success: "Created resource definition: ID 28440 - USB Hub Device"
        Error: Detailed validation error or API error

    Example Usage:
        create_resource_definition("Test USB Hub", 25, discussion="For lab testing")
        create_resource_definition("MacBook Pro", 56, component_id=1234567, priority=2)

    API Endpoint: POST /tests/resourcedefinitions
    Required Permissions: Resource definition creation access
    Schema: CreateResourceDefinitionRequest
    Response: Created resource definition with ID
    """
    try:
        if not title.strip():
            return "Error: title is required"
        if category_id <= 0:
            return "Error: category_id must be positive"
        if component_id <= 0:
            component_id = 1118899  # Default TSTT component
        if class_id <= 0:
            class_id = 1  # Default to hardware class
        if priority < 1 or priority > 5:
            priority = 5  # Default priority

        # Build the request payload using correct API schema
        payload = {
            "title": title.strip(),
            "categoryId": category_id,
            "classId": class_id,
            "componentId": component_id,
            "priority": priority
        }

        if discussion.strip():
            payload["discussion"] = discussion.strip()

        # Use direct radar_request call for POST /tests/resourcedefinitions
        import core.resource_core as tsttresource_core

        response = tsttresource_core.radar_request(
            '/tests/resourcedefinitions',
            'POST',
            {'Content-Type': 'application/json'},  # headers
            payload,  # body
            token=radarclient.radar_token.token,
            timeout=60
        )

        # Check if creation was successful (HTTP 201 Created expected)
        if hasattr(response, 'status_code') and response.status_code == 201:
            # Parse the response to get the created resource definition ID
            if hasattr(response, 'json') and callable(response.json):
                try:
                    result = response.json()
                    resdef_id = result.get("id", "unknown")
                    return f"Created resource definition: ID {resdef_id} - {title}"
                except:
                    return f"Created resource definition: {title} (ID unknown)"
            else:
                return f"Created resource definition: {title} (ID unknown)"
        else:
            return f"Error: API returned status {getattr(response, 'status_code', 'unknown')}: {getattr(response, 'text', 'No details')}"

    except Exception as e:
        return f"Error: failed to create resource definition: {e}"


# @_register_tool  # DISABLED: Delete APIs not accessible - requires special permissions
def delete_resource_definition(resdef_id: int) -> str:
    """Delete a resource definition by ID.

    Parameters:
        resdef_id: The resource definition ID to delete.

    Returns:
        A success message or error message.
    """
    try:
        if resdef_id <= 0:
            return "Error: resdef_id must be positive"

        # Get resource definition title for confirmation message
        try:
            resdef_data = radarclient.resource_definition_for_id(resdef_id)
            if isinstance(resdef_data, dict):
                title = resdef_data.get("title", f"Resource Definition {resdef_id}")
            else:
                title = f"Resource Definition {resdef_id}"
        except Exception:
            title = f"Resource Definition {resdef_id}"

        # Delete the resource definition using direct API call
        import core.resource_core as tsttresource_core

        response = tsttresource_core.radar_request(
            f'/tests/resourcedefinitions/{resdef_id}',
            'DELETE',
            {},  # headers
            None,  # body
            token=radarclient.radar_token.token,
            timeout=60
        )

        # Check if deletion was successful (HTTP 204 No Content expected for DELETE)
        if hasattr(response, 'status_code') and response.status_code == 204:
            return f"Deleted resource definition: ID {resdef_id} ({title})"
        else:
            return f"Error: unexpected response when deleting resource definition {resdef_id}: status={getattr(response, 'status_code', 'unknown')}"

    except Exception as e:
        return f"Error: failed to delete resource definition {resdef_id}: {e}"


@_register_tool
def update_resource_definition(
    resdef_id: int,
    title: str = "",
    category_id: int = 0,
    component_id: int = 0,
    class_id: int = 0,
    priority: int = 0,
    discussion: str = ""
) -> str:
    """Update a resource definition with new field values.

    Parameters:
        resdef_id: The resource definition ID to update (required).
        title: New title (optional).
        category_id: New category ID (optional).
        component_id: New component ID (optional).
        class_id: New class ID (optional).
        priority: New priority 1-5 (optional).
        discussion: New discussion text (optional).

    Returns:
        A success message or error message.
    """
    try:
        if resdef_id <= 0:
            return "Error: resdef_id must be positive"

        # Build the update payload with only provided fields
        payload = {}

        if title.strip():
            payload["title"] = title.strip()

        if category_id > 0:
            payload["categoryId"] = category_id

        if component_id > 0:
            payload["componentId"] = component_id

        if class_id > 0:
            payload["classId"] = class_id

        if priority >= 1 and priority <= 5:
            payload["priority"] = priority

        if discussion.strip():
            payload["discussion"] = discussion.strip()

        if not payload:
            return "Error: at least one field must be provided for update"

        # Get current resource definition title for confirmation message
        try:
            resdef_data = radarclient.resource_definition_for_id(resdef_id)
            if isinstance(resdef_data, dict):
                current_title = resdef_data.get("title", f"Resource Definition {resdef_id}")
            else:
                current_title = f"Resource Definition {resdef_id}"
        except Exception:
            current_title = f"Resource Definition {resdef_id}"

        # Update the resource definition using direct API call
        import core.resource_core as tsttresource_core

        response = tsttresource_core.radar_request(
            f'/tests/resourcedefinitions/{resdef_id}',
            'PUT',
            {'Content-Type': 'application/json'},  # headers
            payload,  # body
            token=radarclient.radar_token.token,
            timeout=60
        )

        # Check if update was successful (HTTP 200 OK or 204 No Content expected)
        if hasattr(response, 'status_code') and response.status_code in [200, 204, 205]:
            updated_fields = list(payload.keys())
            return f"Updated resource definition: ID {resdef_id} ({current_title}) - fields: {', '.join(updated_fields)}"
        else:
            return f"Error: API returned status {getattr(response, 'status_code', 'unknown')}: {getattr(response, 'text', 'No details')}"

    except Exception as e:
        return f"Error: failed to update resource definition {resdef_id}: {e}"


# @_register_tool  # DISABLED: Bulk APIs not accessible - requires special permissions
def bulk_modify_resources(
    resource_ids: str,
    title: str = "",
    priority: int = 0,
    specific_location: str = "",
    component_id: int = 0,
    category_id: int = 0,
    discussion: str = ""
) -> str:
    """Bulk modify multiple resources via POST /tests/resources/modify.

    This function efficiently updates multiple resources with the same field values
    in a single API call, ideal for batch operations.

    Parameters:
        resource_ids: Comma-separated list of resource IDs (required).
                     Example: "286044,286046,298608" or "286044, 286046, 298608"
        title: New title for all resources (optional).
               Example: "Updated Test Device"
        priority: New priority 1-5 for all resources (optional, 0 = no change).
                 1=Highest, 2=High, 3=Medium, 4=Low, 5=Lowest
        specific_location: New specific location for all resources (optional).
                          Example: "Lab A", "Building 1 Room 101"
        component_id: New component ID for all resources (optional, 0 = no change).
                     Example: 1118899 (TSTT)
        category_id: New category ID for all resources (optional, 0 = no change).
                    Example: 25 (Adapter), 56 (Flash Drive)
        discussion: New discussion text for all resources (optional).
                   Example: "Batch updated for testing purposes"

    Bulk Operation Benefits:
        - Single API call for multiple resources
        - Atomic operation (all succeed or all fail)
        - Efficient for large-scale updates
        - Consistent field values across resources

    Returns:
        Success: "Bulk modified 3 resources: 286044, 286046, 298608 - fields: title, priority"
        Error: Permission error or validation failure

    Example Usage:
        bulk_modify_resources("286044,286046", title="Updated Device", priority=3)
        bulk_modify_resources("298608,298610,298612", specific_location="New Lab")

    API Endpoint: POST /tests/resources/modify
    Required Permissions: Bulk Modify Resources endpoint access (special permission)
    Schema: ModifyResourcesRequest (ids array + optional fields)
    Note: This endpoint requires special API permissions - request access via Radar portal
    """
    try:
        if not resource_ids.strip():
            return "Error: resource_ids is required"

        # Parse resource IDs from comma-separated string
        try:
            id_list = []
            for id_str in resource_ids.split(','):
                id_str = id_str.strip()
                if id_str:
                    resource_id = int(id_str)
                    if resource_id > 0:
                        id_list.append(resource_id)

            if not id_list:
                return "Error: no valid resource IDs provided"

        except ValueError as e:
            return f"Error: invalid resource ID format: {e}"

        # Build the update payload with only provided fields
        payload = {
            "ids": id_list
        }

        if title.strip():
            payload["title"] = title.strip()

        if priority >= 1 and priority <= 5:
            payload["priority"] = priority

        if specific_location.strip():
            payload["specificLocation"] = specific_location.strip()

        if component_id > 0:
            payload["componentId"] = component_id

        if category_id > 0:
            payload["categoryId"] = category_id

        if discussion.strip():
            payload["discussion"] = discussion.strip()

        # Check if at least one field besides IDs is provided
        if len(payload) <= 1:  # Only "ids" key
            return "Error: at least one field must be provided for bulk update"

        # Perform bulk modification using direct API call
        import core.resource_core as tsttresource_core

        response = tsttresource_core.radar_request(
            '/tests/resources/modify',
            'POST',
            {'Content-Type': 'application/json'},  # headers
            payload,  # body
            token=radarclient.radar_token.token,
            timeout=120  # Longer timeout for bulk operations
        )

        # Check if bulk modification was successful
        if hasattr(response, 'status_code') and response.status_code in [200, 204, 205]:
            updated_fields = [k for k in payload.keys() if k != "ids"]
            return f"Bulk modified {len(id_list)} resources: {', '.join(map(str, id_list))} - fields: {', '.join(updated_fields)}"
        else:
            return f"Error: API returned status {getattr(response, 'status_code', 'unknown')}: {getattr(response, 'text', 'No details')}"

    except Exception as e:
        return f"Error: failed to bulk modify resources: {e}"


@_register_tool
def get_location_info() -> str:
    """Get comprehensive location information via GET /tests/resources/locations.

    This function retrieves the complete global location hierarchy including
    campuses, zones, and buildings for resource location assignment.

    Parameters:
        None required.

    Returns:
        A detailed location structure containing:
        - Campus names (global Apple locations)
        - Zone names within each campus
        - Building codes and names
        - Complete hierarchy for location selection

    Global Locations Include:
        - Cupertino: Apple Park, Vallco Parkway, Bubb_RW, AC1, AC2, SC_CC
        - International: Singapore, Cork, Shanghai, Tokyo, Germany
        - US Locations: Sunnyvale, Austin, San Diego, Beaverton
        - External Vendors: Shenzhen, Jiaxing, Suzhou, Chengdu

    Location Hierarchy:
        Campus → Zone → Building
        Example: Cupertino → Apple Park → AP01:4

    Returns:
        Success: "Available locations (1):\ncampuses: [{'name': 'Cupertino', 'zones': [...]}]"
        Error: API error message

    Example Usage:
        get_location_info()
        # Use location data for resource specificLocation field
        # Reference buildings like "AP01:4", "bb14", "sgp001"

    API Endpoint: GET /tests/resources/locations
    Required Permissions: Basic location read access
    Response Format: Complex nested structure with campuses/zones/buildings
    Use Case: Location validation for resource creation and updates
    """
    try:
        # Use direct radar_request call for GET /tests/resources/locations
        import core.resource_core as tsttresource_core

        response = tsttresource_core.radar_request(
            '/tests/resources/locations',
            'GET',
            {},  # headers
            None,  # body
            token=radarclient.radar_token.token,
            timeout=60
        )

        # Check if request was successful
        if hasattr(response, 'status_code') and response.status_code == 200:
            # Parse the response
            if hasattr(response, 'json') and callable(response.json):
                try:
                    locations = response.json()
                except:
                    locations = response.text if hasattr(response, 'text') else str(response)
            elif hasattr(response, 'text'):
                try:
                    import json
                    locations = json.loads(response.text)
                except:
                    locations = response.text
            else:
                locations = str(response)

            # Handle different response formats
            if isinstance(locations, dict):
                # Check if it's a wrapper with locations key
                if "locations" in locations:
                    location_list = locations["locations"]
                else:
                    location_list = locations

                lines = []
                if isinstance(location_list, list):
                    for location in location_list:
                        if isinstance(location, dict):
                            loc_id = location.get("id", "unknown")
                            loc_name = location.get("name", "No name")
                            loc_desc = location.get("description", "")
                            campus = location.get("campus", "")
                            if campus:
                                lines.append(f"{loc_id} - {loc_name} (Campus: {campus})")
                            elif loc_desc:
                                lines.append(f"{loc_id} - {loc_name}: {loc_desc}")
                            else:
                                lines.append(f"{loc_id} - {loc_name}")
                        else:
                            lines.append(str(location))
                else:
                    # Handle non-list format
                    for key, value in location_list.items():
                        lines.append(f"{key}: {value}")

                return f"Available locations ({len(lines)}):\n" + "\n".join(lines)

            elif isinstance(locations, list):
                # Direct list format
                lines = []
                for location in locations:
                    if isinstance(location, dict):
                        loc_id = location.get("id", "unknown")
                        loc_name = location.get("name", "No name")
                        loc_desc = location.get("description", "")
                        campus = location.get("campus", "")
                        if campus:
                            lines.append(f"{loc_id} - {loc_name} (Campus: {campus})")
                        elif loc_desc:
                            lines.append(f"{loc_id} - {loc_name}: {loc_desc}")
                        else:
                            lines.append(f"{loc_id} - {loc_name}")
                    else:
                        lines.append(str(location))
                return f"Available locations ({len(lines)}):\n" + "\n".join(lines)
            else:
                # String or other format
                return f"Location information: {locations}"

        else:
            return f"Error: API returned status {getattr(response, 'status_code', 'unknown')}: {getattr(response, 'text', 'No details')}"

    except Exception as e:
        return f"Error: failed to get location info: {e}"


# @_register_tool  # DISABLED: Bulk APIs not accessible - requires special permissions
def bulk_modify_resource_definitions(
    resdef_ids: str,
    title: str = "",
    priority: int = 0,
    component_id: int = 0,
    category_id: int = 0,
    class_id: int = 0,
    discussion: str = ""
) -> str:
    """Bulk modify multiple resource definitions with the same field values.

    Parameters:
        resdef_ids: Comma-separated list of resource definition IDs (required).
        title: New title for all resource definitions (optional).
        priority: New priority 1-5 for all resource definitions (optional).
        component_id: New component ID for all resource definitions (optional).
        category_id: New category ID for all resource definitions (optional).
        class_id: New class ID for all resource definitions (optional).
        discussion: New discussion text for all resource definitions (optional).

    Returns:
        A success message with the number of resource definitions modified or an error message.
    """
    try:
        if not resdef_ids.strip():
            return "Error: resdef_ids is required"

        # Parse resource definition IDs from comma-separated string
        try:
            id_list = []
            for id_str in resdef_ids.split(','):
                id_str = id_str.strip()
                if id_str:
                    resdef_id = int(id_str)
                    if resdef_id > 0:
                        id_list.append(resdef_id)

            if not id_list:
                return "Error: no valid resource definition IDs provided"

        except ValueError as e:
            return f"Error: invalid resource definition ID format: {e}"

        # Build the update payload with only provided fields
        payload = {
            "ids": id_list
        }

        if title.strip():
            payload["title"] = title.strip()

        if priority >= 1 and priority <= 5:
            payload["priority"] = priority

        if component_id > 0:
            payload["componentId"] = component_id

        if category_id > 0:
            payload["categoryId"] = category_id

        if class_id > 0:
            payload["classId"] = class_id

        if discussion.strip():
            payload["discussion"] = discussion.strip()

        # Check if at least one field besides IDs is provided
        if len(payload) <= 1:  # Only "ids" key
            return "Error: at least one field must be provided for bulk update"

        # Perform bulk modification using direct API call
        import core.resource_core as tsttresource_core

        response = tsttresource_core.radar_request(
            '/tests/resourcedefinitions/modify',
            'POST',
            {'Content-Type': 'application/json'},  # headers
            payload,  # body
            token=radarclient.radar_token.token,
            timeout=120  # Longer timeout for bulk operations
        )

        # Check if bulk modification was successful
        if hasattr(response, 'status_code') and response.status_code in [200, 204, 205]:
            updated_fields = [k for k in payload.keys() if k != "ids"]
            return f"Bulk modified {len(id_list)} resource definitions: {', '.join(map(str, id_list))} - fields: {', '.join(updated_fields)}"
        else:
            return f"Error: API returned status {getattr(response, 'status_code', 'unknown')}: {getattr(response, 'text', 'No details')}"

    except Exception as e:
        return f"Error: failed to bulk modify resource definitions: {e}"


@_register_tool
def get_resource_properties() -> str:
    """Get available resource properties via GET /tests/resources/attributes/properties.

    This function retrieves all available resource properties organized by type,
    essential for resource property validation and detailed resource characterization.

    Parameters:
        None required.

    Returns:
        A comprehensive list containing:
        - Property ID (for API calls)
        - Property name (human-readable)
        - Property type/category
        - Total count (typically 112+ properties)

    Property Categories Include:
        - Airport: Hardware Version, Interface Name, RF Capability, Security
        - Bluetooth: BT Class, Power Class, Profile, Chipset, HCI Revision
        - Camera: Model ID, Unique ID
        - Display: ARC Support, Audio Output, Chromaticity, DP/HDMI versions, HDR
        - GPU: Bus, VRAM (Bytes)
        - General: Firmware, ID, MAC Address, Manufacturer, Serial Number, Size, Speed
        - Hard Drive: Block Size, Medium Type, SATA Features, Product Revision
        - Memory: Memory Location, Memory Type
        - Processor: Cache Size, Number of Cores, Number of Processors
        - iOS device: Baseband, ECID, UDID, Device Color, CPU Architecture

    Property Usage:
        - Resource characterization and specification
        - Hardware inventory tracking
        - Device capability documentation
        - Technical specification validation

    Returns:
        Success: "Available resource properties (112):\n27 - Hardware Version (Type: Airport)..."
        Error: API error message

    Example Usage:
        get_resource_properties()
        # Use property IDs for resource property assignments
        # Reference properties like ID 27 (Hardware Version), ID 11 (MAC Address)

    API Endpoint: GET /tests/resources/attributes/properties
    Required Permissions: Basic resource attribute read access
    Response Format: {"properties": [{"id": 27, "name": "Hardware Version", "type": "Airport"}, ...]}
    Use Case: Property validation for detailed resource specifications
    """
    try:
        # Use direct radar_request call for GET /tests/resources/attributes/properties
        import core.resource_core as tsttresource_core

        response = tsttresource_core.radar_request(
            '/tests/resources/attributes/properties',
            'GET',
            {},  # headers
            None,  # body
            token=radarclient.radar_token.token,
            timeout=60
        )

        # Check if request was successful
        if hasattr(response, 'status_code') and response.status_code == 200:
            # Parse the response
            if hasattr(response, 'json') and callable(response.json):
                try:
                    properties = response.json()
                except:
                    properties = response.text if hasattr(response, 'text') else str(response)
            elif hasattr(response, 'text'):
                try:
                    import json
                    properties = json.loads(response.text)
                except:
                    properties = response.text
            else:
                properties = str(response)

            # Handle different response formats
            if isinstance(properties, dict) and "properties" in properties:
                # API returns {"properties": [...]} format
                property_list = properties["properties"]
                lines = []
                for prop in property_list:
                    if isinstance(prop, dict):
                        prop_id = prop.get("id", "unknown")
                        prop_name = prop.get("name", prop.get("value", "No name"))
                        prop_type = prop.get("type", "")
                        if prop_type:
                            lines.append(f"{prop_id} - {prop_name} (Type: {prop_type})")
                        else:
                            lines.append(f"{prop_id} - {prop_name}")
                    else:
                        lines.append(str(prop))
                return f"Available resource properties ({len(lines)}):\n" + "\n".join(lines)
            elif isinstance(properties, list):
                # Direct list of property objects
                lines = []
                for prop in properties:
                    if isinstance(prop, dict):
                        prop_id = prop.get("id", "unknown")
                        prop_name = prop.get("name", prop.get("value", "No name"))
                        prop_type = prop.get("type", "")
                        if prop_type:
                            lines.append(f"{prop_id} - {prop_name} (Type: {prop_type})")
                        else:
                            lines.append(f"{prop_id} - {prop_name}")
                    else:
                        lines.append(str(prop))
                return f"Available resource properties ({len(lines)}):\n" + "\n".join(lines)
            else:
                # Unexpected format
                return f"Resource properties response: {properties}"

        else:
            return f"Error: API returned status {getattr(response, 'status_code', 'unknown')}: {getattr(response, 'text', 'No details')}"

    except Exception as e:
        return f"Error: failed to get resource properties: {e}"


@_register_tool
def get_all_resource_attributes() -> str:
    """Get all resource attributes (categories, properties, and lookup categories).

    Returns:
        A formatted summary of all available resource attributes or an error message.
    """
    try:
        # Use direct radar_request call for GET /tests/resources/attributes
        import core.resource_core as tsttresource_core

        response = tsttresource_core.radar_request(
            '/tests/resources/attributes',
            'GET',
            {},  # headers
            None,  # body
            token=radarclient.radar_token.token,
            timeout=60
        )

        # Check if request was successful
        if hasattr(response, 'status_code') and response.status_code == 200:
            # Parse the response
            if hasattr(response, 'json') and callable(response.json):
                try:
                    attributes = response.json()
                except:
                    attributes = response.text if hasattr(response, 'text') else str(response)
            elif hasattr(response, 'text'):
                try:
                    import json
                    attributes = json.loads(response.text)
                except:
                    attributes = response.text
            else:
                attributes = str(response)

            # Handle the response format
            if isinstance(attributes, dict):
                lines = []

                # Process categories
                if "categories" in attributes:
                    categories = attributes["categories"]
                    if isinstance(categories, list):
                        lines.append(f"📂 Categories ({len(categories)}):")
                        for i, cat in enumerate(categories[:5]):  # Show first 5
                            if isinstance(cat, dict):
                                cat_name = cat.get("value", cat.get("name", "Unknown"))
                                lines.append(f"   {i+1}. {cat_name}")
                        if len(categories) > 5:
                            lines.append(f"   ... and {len(categories) - 5} more")

                # Process properties
                if "properties" in attributes:
                    properties = attributes["properties"]
                    if isinstance(properties, list):
                        lines.append(f"\n🔧 Properties ({len(properties)}):")
                        for i, prop in enumerate(properties[:5]):  # Show first 5
                            if isinstance(prop, dict):
                                prop_name = prop.get("name", prop.get("value", "Unknown"))
                                lines.append(f"   {i+1}. {prop_name}")
                        if len(properties) > 5:
                            lines.append(f"   ... and {len(properties) - 5} more")

                # Process lookup categories
                if "lookupCategories" in attributes:
                    lookup_cats = attributes["lookupCategories"]
                    if isinstance(lookup_cats, list):
                        lines.append(f"\n🔍 Lookup Categories ({len(lookup_cats)}):")
                        for i, lookup in enumerate(lookup_cats[:5]):  # Show first 5
                            if isinstance(lookup, dict):
                                lookup_name = lookup.get("name", lookup.get("value", "Unknown"))
                                lines.append(f"   {i+1}. {lookup_name}")
                        if len(lookup_cats) > 5:
                            lines.append(f"   ... and {len(lookup_cats) - 5} more")

                # Process any other top-level keys
                other_keys = [k for k in attributes.keys() if k not in ["categories", "properties", "lookupCategories"]]
                if other_keys:
                    lines.append(f"\n📋 Other Attributes: {', '.join(other_keys)}")

                if lines:
                    return "All Resource Attributes:\n" + "\n".join(lines)
                else:
                    return f"Resource attributes response (no recognized structure): {attributes}"
            else:
                return f"Resource attributes response: {attributes}"

        else:
            return f"Error: API returned status {getattr(response, 'status_code', 'unknown')}: {getattr(response, 'text', 'No details')}"

    except Exception as e:
        return f"Error: failed to get all resource attributes: {e}"


@_register_tool
def get_location_spaces(
    campus: str = "",
    zone: str = "",
    building: str = ""
) -> str:
    """Get detailed location information by campus, zone, and building.

    Parameters:
        campus: Campus name to filter by (optional).
        zone: Zone name to filter by (optional).
        building: Building name to filter by (optional).

    Returns:
        A formatted list of location spaces or an error message.
    """
    try:
        # Build the request payload
        payload = {}

        if campus.strip():
            payload["campus"] = campus.strip()

        if zone.strip():
            payload["zone"] = zone.strip()

        if building.strip():
            payload["building"] = building.strip()

        # Use direct radar_request call for POST /tests/resources/locations/spaces
        import core.resource_core as tsttresource_core

        response = tsttresource_core.radar_request(
            '/tests/resources/locations/spaces',
            'POST',
            {'Content-Type': 'application/json'},  # headers
            payload,  # body
            token=radarclient.radar_token.token,
            timeout=60
        )

        # Check if request was successful
        if hasattr(response, 'status_code') and response.status_code == 200:
            # Parse the response
            if hasattr(response, 'json') and callable(response.json):
                try:
                    spaces = response.json()
                except:
                    spaces = response.text if hasattr(response, 'text') else str(response)
            elif hasattr(response, 'text'):
                try:
                    import json
                    spaces = json.loads(response.text)
                except:
                    spaces = response.text
            else:
                spaces = str(response)

            # Handle different response formats
            if isinstance(spaces, dict) and "spaces" in spaces:
                # API returns {"spaces": [...]} format
                space_list = spaces["spaces"]
                lines = []
                for space in space_list:
                    if isinstance(space, dict):
                        space_id = space.get("id", "unknown")
                        space_name = space.get("name", "No name")
                        space_type = space.get("type", "")
                        space_campus = space.get("campus", "")
                        space_building = space.get("building", "")

                        location_info = []
                        if space_campus:
                            location_info.append(f"Campus: {space_campus}")
                        if space_building:
                            location_info.append(f"Building: {space_building}")
                        if space_type:
                            location_info.append(f"Type: {space_type}")

                        if location_info:
                            lines.append(f"{space_id} - {space_name} ({', '.join(location_info)})")
                        else:
                            lines.append(f"{space_id} - {space_name}")
                    else:
                        lines.append(str(space))
                return f"Location spaces ({len(lines)}):\n" + "\n".join(lines)
            elif isinstance(spaces, list):
                # Direct list format
                lines = []
                for space in spaces:
                    if isinstance(space, dict):
                        space_id = space.get("id", "unknown")
                        space_name = space.get("name", "No name")
                        space_type = space.get("type", "")
                        lines.append(f"{space_id} - {space_name} (Type: {space_type})" if space_type else f"{space_id} - {space_name}")
                    else:
                        lines.append(str(space))
                return f"Location spaces ({len(lines)}):\n" + "\n".join(lines)
            else:
                # Unexpected format
                return f"Location spaces response: {spaces}"

        else:
            return f"Error: API returned status {getattr(response, 'status_code', 'unknown')}: {getattr(response, 'text', 'No details')}"

    except Exception as e:
        return f"Error: failed to get location spaces: {e}"


@_register_tool
def update_resource(
    resource_id: int,
    title: str = "",
    priority: int = -1,
    specific_location: str = "",
) -> str:
    """Update fields of a Radar resource.

    Parameters:
        resource_id: The resource ID to update.
        title: Optional new title.
        priority: Optional new priority (>= 0 to apply).
        specific_location: Optional new specificLocation value.

    Returns:
        A summary string with rdar:// link and changed fields, or an error message.
    """
    try:
        if resource_id <= 0:
            return "Error: resource_id must be positive"

        # Get current resource data first (like Python script)
        try:
            current_resource = radarclient.resource_for_id(resource_id)
            if isinstance(current_resource, dict):
                current_title = current_resource.get("title") or current_resource.get("resource", {}).get("title") or f"Resource {resource_id}"
            else:
                current_title = f"Resource {resource_id}"
        except Exception:
            current_title = f"Resource {resource_id}"

        # Process title like Python script (Resource.py lines 81-110)
        final_title = title if title else current_title

        # Extract resdef from title if needed for priority lookup
        if priority == -1:
            _, speed_resdef = _clean_title(final_title)
            extracted_resdef = _extract_resdef(speed_resdef)
            if extracted_resdef:
                try:
                    priority_from_resdef, _ = _extract_title_priority(int(extracted_resdef))
                    if priority_from_resdef:
                        priority = int(priority_from_resdef)
                except Exception:
                    pass

        # Set default priority if still not determined
        if priority == -1:
            priority = 5  # DEFAULT_PRIORITY

        # Build payload matching Python script (Resource.py lines 121-133)
        payload = {
            "title": final_title.strip(),
            "componentId": 1118899,         # DEFAULT_COMPONENT_ID
            "priority": priority + 1,       # API expects priority + 1 (incremented)
            "locationId": 66842,            # DEFAULT_LOCATION_ID
            "driId": 973776146,             # DEFAULT_DRI_ID
            "assigneeId": 1555131384,       # DEFAULT_ASSIGNEE_ID
        }

        # Add specificLocation if provided
        if specific_location:
            payload["specificLocation"] = specific_location

        radarclient.update_resource(resource_id, payload)
        return f"Updated resource: rdar://res/{resource_id}"
    except Exception as e:
        return f"Error: failed to update resource {resource_id}: {e}"



# --- Step 5: Define Resource Definition tools ---

@_register_tool
def search_resource_definitions(title_substr: str) -> str:
    """Search resource definitions by title substring and return a short list.

    Parameters:
        title_substr: A substring to search in resource definition titles.

    Returns:
        Lines of "<id> - <title>", or an error message.
    """
    try:
        criteria = {"title": f"%{title_substr}%"}
        results = radarclient.find_resource_definitions(criteria)
        if not results:
            return "No resource definitions found"
        lines = []
        for r in results[:20]:  # limit to top 20
            rid = None
            title = None
            if isinstance(r, dict):
                rid = r.get("id") or r.get("resourceDefinition", {}).get("id")
                title = r.get("title") or r.get("resourceDefinition", {}).get("title")
            lines.append(f"{rid} - {title}")
        return "\n".join(lines)
    except Exception as e:
        return f"Error: failed to search resource definitions: {e}"


@_register_tool
def get_resource_definition_by_id(resdef_id: int) -> str:
    """Get details of a resource definition by ID from the Radar test suite.

    This function retrieves information about a specific resource definition using the
    existing radarclient.resource_definition_for_id() method.

    Parameters:
        resdef_id: Resource definition ID (positive integer).
                  Example: 27776, 28440, 28442

    Returns:
        A formatted string containing:
        - Resource definition ID
        - Title/name of the resource definition
        - Associated component information
        - Category and class details

    Example Usage:
        get_resource_definition_by_id(27776)
        # Returns: "ResDef 27776: title='USB Hub Device'"

    API Endpoint: Uses existing radarclient method
    Required Permissions: Basic resource definition read access
    """
    try:
        data = radarclient.resource_definition_for_id(resdef_id)
        title = None
        if isinstance(data, dict):
            title = data.get("title") or data.get("resourceDefinition", {}).get("title")
        title = title or "(no title)"
        return f"ResDef {resdef_id}: title='{title}'"
    except Exception as e:
        return f"Error: failed to fetch resource definition {resdef_id}: {e}"


@_register_tool
def attach_resource_to_resdef(resource_id: int, resdef_id: int) -> str:
    """Attach an existing resource to a resource definition.

    Parameters:
        resource_id: The resource ID to attach.
        resdef_id: The resource definition ID to attach to.

    Returns:
        A success message or error message.
    """
    # Get resource title for logging (like Python res2resdef.py line 32)
    try:
        resource_data = radarclient.resource_for_id(resource_id)
        if isinstance(resource_data, dict):
            title = resource_data.get("title") or resource_data.get("resource", {}).get("title") or f"Resource {resource_id}"
        else:
            title = f"Resource {resource_id}"
    except Exception:
        title = f"Resource {resource_id}"

    # Use the internal helper function for the actual attachment
    success, message = _attach_resource_to_resdef_internal(resource_id, resdef_id)

    if success:
        return f"Resource rdar://res/{resource_id} ({title}) attached to resdef {resdef_id}"
    else:
        return f"Error: {message}"


# --- Step 6: Define Resource Field tools ---

@_register_tool
def get_resource_fields(resource_id: int, fields: list[str]) -> str:
    """Get selected fields for a resource.

    Parameters:
        resource_id: Resource ID.
        fields: List of field names to request.

    Returns:
        Comma-separated key=value pairs, or an error message.
    """
    try:
        if resource_id <= 0:
            return "Error: resource_id must be positive"
        if not fields:
            return "Error: fields must be a non-empty list"
        data = radarclient.fields_for_resource_id(resource_id, request_fields=fields)
        if not isinstance(data, dict):
            return f"Error: unexpected response type: {type(data)}"
        parts = []
        for k in fields:
            v = data.get(k, "")
            parts.append(f"{k}={v}")
        return f"Resource {resource_id}: " + ", ".join(parts)
    except Exception as e:
        return f"Error: failed to get fields for resource {resource_id}: {e}"


@_register_tool
def update_resource_fields(resource_id: int, fields: dict[str, object]) -> str:
    """Update selected fields for a resource.

    Parameters:
        resource_id: Resource ID.
        fields: Mapping of field name to new value.

    Returns:
        A summary string with updated keys, or an error message.
    """
    try:
        if resource_id <= 0:
            return "Error: resource_id must be positive"
        if not fields:
            return "Error: fields must be a non-empty mapping"
        radarclient.update_resource(resource_id, fields)
        return f"Updated resource {resource_id}: fields={list(fields.keys())}"
    except Exception as e:
        return f"Error: failed to update fields for resource {resource_id}: {e}"


# --- Step 7: Optional utility tools ---

@_register_tool
def generate_print_links(resource_ids: list[int]) -> str:
    """Generate combined and per-item rdar:// links for resources.

    Parameters:
        resource_ids: List of resource IDs.

    Returns:
        A summary string with a combined link and per-line links.
    """
    try:
        if not resource_ids:
            return "Error: resource_ids must be non-empty"
        combined = ",".join(str(x) for x in resource_ids)
        per_line = "\n".join(f"rdar://res/{x}" for x in resource_ids)
        return f"Combined: rdar://res/{combined}\n{per_line}"
    except Exception as e:
        return f"Error: failed to generate links: {e}"


# --- Step 10: Server startup entrypoint (minimal) ---

def main():
    """CLI entrypoint.

    - Always prints a health summary first
    - If FastMCP is available, starts the MCP server on stdio and blocks
    """
    print(_health_summary())
    if mcp is not None and FastMCP is not None:
        # Start MCP stdio server; this will block
        try:
            mcp.run()  # type: ignore[attr-defined]
        except Exception as e:
            # Keep a readable error message on stdout for host tooling
            print(f"Error: failed to start MCP server: {e}")


if __name__ == "__main__":
    main()
