#!/bin/bash

# Resource Manager Distribution Package Creator
# This script creates a distribution package for the Resource Manager application

set -e  # Exit on any error

# Configuration
APP_NAME="ResourceManager"
VERSION="1.0.0"
DIST_DIR="$HOME/Desktop/${APP_NAME}_Distribution_v${VERSION}"
BUILD_DIR="$HOME/Library/Developer/Xcode/DerivedData/ResourceManager-*/Build/Products/Release"

echo "🚀 Creating Resource Manager Distribution Package"
echo "=================================================="

# Clean up any existing distribution directory
if [ -d "$DIST_DIR" ]; then
    echo "🧹 Cleaning up existing distribution directory..."
    rm -rf "$DIST_DIR"
fi

# Create distribution directory
echo "📁 Creating distribution directory: $DIST_DIR"
mkdir -p "$DIST_DIR"

# Find the most recent build
echo "🔍 Looking for Release build..."
BUILD_PATH=$(find $BUILD_DIR -name "${APP_NAME}.app" -type d 2>/dev/null | head -1)

if [ -z "$BUILD_PATH" ]; then
    echo "❌ Error: Could not find ${APP_NAME}.app in Release build directory"
    echo "   Please build the project in Release configuration first:"
    echo "   xcodebuild -project ResourceManager.xcodeproj -scheme ResourceManager -configuration Release build"
    exit 1
fi

echo "✅ Found application at: $BUILD_PATH"

# Copy the application
echo "📦 Copying application to distribution directory..."
cp -R "$BUILD_PATH" "$DIST_DIR/"

# Verify the copy
if [ ! -d "$DIST_DIR/${APP_NAME}.app" ]; then
    echo "❌ Error: Failed to copy application"
    exit 1
fi

# Copy documentation
echo "📄 Copying documentation..."
if [ -f "README.md" ]; then
    cp "README.md" "$DIST_DIR/"
else
    echo "⚠️  Warning: README.md not found in current directory"
fi

# Create CSV templates
echo "📊 Creating CSV templates..."
mkdir -p "$DIST_DIR/CSV_Templates"

# Create Resource Creation Template
cat > "$DIST_DIR/CSV_Templates/Resource_Creation_Template.csv" << 'EOF'
title,priority,quantity,location,categoryId
"Sample Resource Title",3,1,"New York",""
"Another Resource",5,2,"California","CAT123"
"Test Resource",4,1,"Texas",""
EOF

# Create Resource Update Template
cat > "$DIST_DIR/CSV_Templates/Resource_Update_Template.csv" << 'EOF'
resourceID,title,priority,location
"12345","Updated Resource Title",4,"Updated Location"
"67890","Another Updated Resource",2,"Boston"
"11111","Final Test Resource",5,"Seattle"
EOF

# Create Installation Instructions
cat > "$DIST_DIR/INSTALLATION.txt" << 'EOF'
Resource Manager - Installation Instructions
==========================================

INSTALLATION:
1. Copy ResourceManager.app to your /Applications folder
2. Right-click on ResourceManager.app and select "Open"
3. Click "Open" when macOS asks for confirmation (first launch only)
4. Grant necessary permissions when prompted

FIRST RUN:
- The application may request network and file system permissions
- These are required for API communication and CSV file operations
- Click "Allow" or "OK" for all permission requests

SYSTEM REQUIREMENTS:
- macOS 14.0 (Sonoma) or later
- Internet connection for API operations
- Sufficient disk space for CSV file operations

TROUBLESHOOTING:
- If the app won't open, try right-clicking and selecting "Open"
- For permission issues, check System Preferences > Security & Privacy
- Refer to README.md for detailed usage instructions

SUPPORT:
- Check README.md for comprehensive documentation
- Review application logs for troubleshooting information
- Contact your system administrator for API-related issues
EOF

# Verify code signature
echo "🔐 Verifying code signature..."
codesign -dv --verbose=4 "$DIST_DIR/${APP_NAME}.app" > "$DIST_DIR/CODE_SIGNATURE_INFO.txt" 2>&1

# Create distribution info
cat > "$DIST_DIR/DISTRIBUTION_INFO.txt" << EOF
Resource Manager Distribution Package
====================================

Version: $VERSION
Build Date: $(date)
Architecture: $(uname -m)
macOS Version: $(sw_vers -productVersion)

Package Contents:
- ResourceManager.app (Main application)
- README.md (Comprehensive documentation)
- INSTALLATION.txt (Installation instructions)
- CSV_Templates/ (Sample CSV files)
- CODE_SIGNATURE_INFO.txt (Code signing details)

Application Details:
- Bundle Identifier: com.resourcemanager.app
- Minimum macOS: 14.0 (Sonoma)
- Code Signature: Ad-hoc (for local distribution)

For enterprise distribution, the application should be properly
code signed with a valid Developer ID certificate.
EOF

# Calculate package size
PACKAGE_SIZE=$(du -sh "$DIST_DIR" | cut -f1)

echo ""
echo "✅ Distribution package created successfully!"
echo "📍 Location: $DIST_DIR"
echo "📏 Package Size: $PACKAGE_SIZE"
echo ""
echo "📋 Package Contents:"
echo "   • ResourceManager.app"
echo "   • README.md"
echo "   • INSTALLATION.txt"
echo "   • CSV_Templates/"
echo "   • CODE_SIGNATURE_INFO.txt"
echo "   • DISTRIBUTION_INFO.txt"
echo ""
echo "🎯 Next Steps:"
echo "   1. Test the application from the distribution directory"
echo "   2. Create a ZIP archive for easy distribution"
echo "   3. Share with end users along with installation instructions"
echo ""

# Offer to create ZIP archive
read -p "📦 Create ZIP archive for distribution? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    ZIP_NAME="${APP_NAME}_v${VERSION}_$(date +%Y%m%d).zip"
    echo "🗜️  Creating ZIP archive: $ZIP_NAME"
    cd "$(dirname "$DIST_DIR")"
    zip -r "$ZIP_NAME" "$(basename "$DIST_DIR")" > /dev/null
    echo "✅ ZIP archive created: $(dirname "$DIST_DIR")/$ZIP_NAME"
    echo "📏 Archive Size: $(du -sh "$(dirname "$DIST_DIR")/$ZIP_NAME" | cut -f1)"
fi

echo ""
echo "🎉 Distribution package ready for deployment!"
