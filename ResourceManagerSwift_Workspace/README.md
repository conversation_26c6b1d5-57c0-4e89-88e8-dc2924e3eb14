# Resource Manager for macOS

A native macOS application for managing resources through API integration, built with Swift and AppKit.

## Features

### 🚀 Resource Creation
- **Single Resource Creation**: Create individual resources with title or ResdefID
- **Batch CSV Creation**: Import and create multiple resources from CSV files
- **Smart Resource Definition**: Automatically fetch resource definitions from API
- **Category Support**: Handle category ID requirements for specific resource types
- **Quantity Control**: Create multiple instances of the same resource
- **Automatic attach resource to resource definition**: Automatically attach resource to resource definition after creation

### 📊 Resource Update
- **CSV-based Updates**: Update existing resources using CSV files with resourceID mapping
- **Template Download**: Get CSV templates for batch operations


### 🖨️ Preview & Print
- **Preview Window**: Interactive preview with editable data before printing
- **Print Resources**: Generate formatted CSV reports for created/updated resources

### 🔗 Enhanced User Experience
- **Clickable Resource Links**: Direct access to resource pages via hyperlinks
- **Progress Feedback**: Real-time operation status and results
- **Input Validation**: Smart form validation and error prevention
- **Clean Interface**: Native macOS design with intuitive controls

## System Requirements

- **macOS**: 14.0 (Sonoma) or later
- **Architecture**: Apple Silicon (ARM64) or Intel (x86_64)
- **Network**: Internet connection for API operations
- **Permissions**: File system access for CSV import/export

## Installation

### Option 1: Pre-built Application
1. Download `ResourceManager.app` from the distribution package
2. Move the application to your `/Applications` folder
3. Right-click and select "Open" to bypass Gatekeeper (first launch only)
4. Grant necessary permissions when prompted

### Option 2: Build from Source
1. Clone or download the project
2. Open `ResourceManager.xcodeproj` in Xcode 15.0+
3. Select your target device/simulator
4. Build and run (⌘+R)

## Usage Guide

### Creating Resources

#### Single Resource Creation
1. **Title or ResdefID**: Enter either a descriptive title or specific ResdefID
2. **Priority**: Select priority level (1-5, default: 5)
3. **Quantity**: Specify number of resources to create (default: 1)
4. **Location**: Set resource location (default: "Aruba")
5. **Category ID**: Optional, will be prompted if required
6. Click **Create Resource**

#### CSV Batch Creation
1. Click **Select CSV File** to choose your input file
2. Ensure CSV contains required columns: `title`, `priority`, `quantity`, `location`
3. Review the preview if available
4. Click **Create Resource** to process the batch
5. Monitor progress in the log area

### Updating Resources

#### CSV-based Updates
1. Click **Update CSV** to select your update file
2. CSV must contain `resourceID` column plus fields to update
3. System will fetch existing data and merge updates
4. Review results in the preview window
5. Confirm changes before applying

### Print Resources
1. After creating or updating resources, click **Print Resources**
2. Review data in the interactive preview window
3. Edit any fields directly in the preview table
4. Click **Print** to generate CSV report
5. File automatically saves to ~/Downloads

### Template Management
1. Click **Download Template** to get CSV format examples
2. Templates include all required column headers
3. Use templates as starting point for batch operations

## CSV File Formats

### Creation Template
```csv
title,priority,quantity,location,categoryId
"Sample Resource",3,1,"New York",""
"Another Resource",5,2,"California","CAT123"
```

### Update Template
```csv
resourceID,title,priority,location
"12345","Updated Title",4,"Updated Location"
"67890","Another Update",2,"Boston"
```

### Print Output Format
```csv
Title,Label Title,Qty,speed,resdef,Pri,resourceID,res_link,resdef_link,location,Priority
"Resource Title","Label","1","normal","RES001",3,"12345","rdar://res/12345","rdar://resdef/RES001","New York","3"
```

## API Integration

The application integrates with resource management APIs for:
- **Resource Definition Lookup**: Fetch complete resource information
- **Resource Creation**: Create new resources with full validation
- **Resource Updates**: Modify existing resources by ID
- **Category Management**: Handle category-specific requirements

### Error Handling
- **Network Issues**: Automatic retry and user notification
- **API Errors**: Detailed error messages with suggested actions
- **Validation Failures**: Input validation with helpful guidance
- **Permission Issues**: Clear instructions for resolution

## Troubleshooting

### Common Issues

**"Category ID Required" Error**
- Some resource types require specific category IDs
- Application will prompt for category ID when needed
- Contact your system administrator for valid category IDs

**CSV Import Failures**
- Verify CSV format matches template requirements
- Check for special characters or encoding issues
- Ensure all required columns are present

**Network Connection Issues**
- Verify internet connectivity
- Check firewall settings
- Confirm API endpoints are accessible

**File Permission Errors**
- Grant file system access when prompted
- Check ~/Downloads folder permissions
- Ensure sufficient disk space

### Debug Information
- Application logs operations in the main window
- Resource links are clickable for direct access
- Error messages include specific details for troubleshooting

## Security & Privacy

### Permissions Required
- **Network Access**: For API communication
- **File System Access**: For CSV import/export operations
- **Downloads Folder**: For saving generated reports

### Data Handling
- No sensitive data is stored locally
- All operations use secure API connections
- User data is processed in memory only

## Support

### Getting Help
1. Check this README for common solutions
2. Review error messages for specific guidance
3. Use the application's built-in logging for troubleshooting
4. Contact your system administrator for API-related issues

### Reporting Issues
When reporting problems, please include:
- macOS version and hardware details
- Specific error messages from the application
- Steps to reproduce the issue
- Sample CSV files (if applicable)

## Version History

### Current Version
- ✅ Native macOS application with AppKit
- ✅ Complete resource creation and management
- ✅ CSV import/export functionality
- ✅ Interactive preview windows
- ✅ Clickable resource links
- ✅ Comprehensive error handling
- ✅ Clean, intuitive user interface

---

**Resource Manager** - Streamlined resource management for macOS
