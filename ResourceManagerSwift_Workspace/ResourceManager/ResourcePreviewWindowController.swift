import Cocoa
import CoreImage
import CoreImage.CIFilterBuiltins



class ResourcePreviewWindowController: NSWindowController, NSTextFieldDelegate {
    
    // MARK: - Outlets
    @IBOutlet weak var tableView: NSTableView!
    @IBOutlet weak var titleLabel: NSTextField!
    
    // MARK: - Properties
    private var resources: [ProcessedResourceWithExtras] = []
    private var tableData: [[String]] = []
    private let columnHeaders = [
        "Title*", "Label Title", "Qty", "speed", "resdef*",
        "Pri", "resourceID*", "res_link", "resdef_link", "location", "Priority", "QR Code"
    ]

    // Callback to save edited data when window closes
    var onDataChanged: (([ProcessedResourceWithExtras]) -> Void)?

    // Callback to notify when window is closing
    var onWindowClosing: (() -> Void)?
    
    // MARK: - Initialization
    
    convenience init(resources: [ProcessedResourceWithExtras]) {
        self.init(windowNibName: "ResourcePreviewWindow")
        self.resources = resources
        self.prepareTableData()
    }
    
    override func windowDidLoad() {
        super.windowDidLoad()

        setupWindow()
        setupTableView()
        prepareTableData()
        loadTableData()

        // Set window delegate to capture close events
        window?.delegate = self
    }
    




    // MARK: - Setup Methods
    
    private func setupWindow() {
        window?.title = "Resource Data Preview & Edit"
        window?.setContentSize(NSSize(width: 1200, height: 600))
        window?.center()

        titleLabel?.stringValue = "Preview and edit \(resources.count) resource(s) - Close window to save changes"

        window?.delegate = self
    }
    
    private func setupTableView() {
        // Create columns for each header
        for (index, header) in columnHeaders.enumerated() {
            let column = NSTableColumn(identifier: NSUserInterfaceItemIdentifier("column\(index)"))
            column.title = header
            column.isEditable = true

            // Set column widths based on content type
            switch header {
            case "Title*":
                column.width = 300
            case "Label Title":
                column.width = 250
            case "res_link", "resdef_link":
                column.width = 150
            case "location":
                column.width = 100
            case "QR Code":
                column.width = 120
                column.isEditable = false  // QR codes are not editable
            default:
                column.width = 80
            }

            tableView.addTableColumn(column)
        }

        tableView.dataSource = self
        tableView.delegate = self
        tableView.allowsColumnReordering = false
        tableView.allowsColumnResizing = true
        tableView.allowsMultipleSelection = false
        tableView.allowsEmptySelection = true

        // Set explicit row height to ensure rows are visible (2x original height for better visibility)
        tableView.rowHeight = 48
        tableView.intercellSpacing = NSSize(width: 1, height: 2)

        // Force table view to be view-based
        tableView.usesAlternatingRowBackgroundColors = true

        // Ensure table view has proper sizing
        tableView.autoresizingMask = [.width, .height]
        tableView.columnAutoresizingStyle = .uniformColumnAutoresizingStyle

        // Force a manual refresh after a short delay to ensure everything is set up
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.tableView.reloadData()
            self.tableView.needsLayout = true
            self.tableView.layoutSubtreeIfNeeded()
        }
    }
    
    private func prepareTableData() {
        tableData = []

        for (_, resource) in resources.enumerated() {
            let title = resource.title
            let labelTitle = resource.labelTitle
            let qty = "" // Empty as in original implementation
            let speed = resource.speed
            let resdefId = resource.resdefId ?? ""
            let priority = String(resource.priority)
            let resourceId = resource.resourceId
            let resourceLink = resource.resourceLink
            let resdefLink = !resdefId.isEmpty ? "rdar://resdef/\(resdefId)" : ""
            let location = resource.location ?? ""
            let priorityText = resource.priority == 5 ? "N/A" : String(resource.priority - 1)

            let row = [
                title, labelTitle, qty, speed, resdefId,
                priority, resourceId, resourceLink, resdefLink, location, priorityText, "QR"
            ]
            tableData.append(row)
        }
    }
    
    private func loadTableData() {
        tableView.reloadData()

        // Force layout update to ensure row height changes take effect
        DispatchQueue.main.async {
            self.tableView.noteHeightOfRows(withIndexesChanged: IndexSet(0..<self.tableData.count))
            self.tableView.needsLayout = true
            self.tableView.layoutSubtreeIfNeeded()
        }
    }
    


}

// MARK: - NSTableViewDataSource

extension ResourcePreviewWindowController: NSTableViewDataSource {

    func numberOfRows(in tableView: NSTableView) -> Int {
        return tableData.count
    }
}

// MARK: - NSTableViewDelegate

extension ResourcePreviewWindowController: NSTableViewDelegate {

    // Ensure consistent row height
    func tableView(_ tableView: NSTableView, heightOfRow row: Int) -> CGFloat {
        return 48.0  // 2x original height for better visibility
    }

    func tableView(_ tableView: NSTableView, viewFor tableColumn: NSTableColumn?, row: Int) -> NSView? {
        guard let column = tableColumn,
              let columnIndex = tableView.tableColumns.firstIndex(of: column),
              row < tableData.count,
              columnIndex < tableData[row].count else {
            return nil
        }

        let cellIdentifier = NSUserInterfaceItemIdentifier("cell\(columnIndex)")

        // Check if this is the QR Code column
        if columnIndex == columnHeaders.count - 1 { // Last column is QR Code
            var qrCellView = tableView.makeView(withIdentifier: cellIdentifier, owner: self) as? NSTableCellView

            if qrCellView == nil {
                qrCellView = NSTableCellView()
                qrCellView?.identifier = cellIdentifier

                // Create QR code image view
                let imageView = NSImageView()
                imageView.imageScaling = .scaleProportionallyUpOrDown
                imageView.imageAlignment = .alignCenter
                imageView.translatesAutoresizingMaskIntoConstraints = false

                qrCellView?.addSubview(imageView)
                qrCellView?.imageView = imageView

                // Set up constraints for image view
                NSLayoutConstraint.activate([
                    imageView.leadingAnchor.constraint(equalTo: qrCellView!.leadingAnchor, constant: 4),
                    imageView.trailingAnchor.constraint(equalTo: qrCellView!.trailingAnchor, constant: -4),
                    imageView.topAnchor.constraint(equalTo: qrCellView!.topAnchor, constant: 2),
                    imageView.bottomAnchor.constraint(equalTo: qrCellView!.bottomAnchor, constant: -2)
                ])
            }

            // Generate QR code for the resource link (column 7 is res_link)
            if row < tableData.count && tableData[row].count > 7 {
                let resourceLink = tableData[row][7] // res_link column
                if !resourceLink.isEmpty {
                    if let qrImage = generateQRCode(from: resourceLink) {
                        qrCellView?.imageView?.image = qrImage
                    } else {
                        // Use system QR code symbol as fallback
                        qrCellView?.imageView?.image = NSImage(systemSymbolName: "qrcode", accessibilityDescription: "QR Code")
                    }
                } else {
                    qrCellView?.imageView?.image = nil
                }
            }

            return qrCellView
        }

        // Use a simple NSTableCellView with default text field for other columns
        var cellView = tableView.makeView(withIdentifier: cellIdentifier, owner: self) as? NSTableCellView

        if cellView == nil {
            cellView = NSTableCellView()
            cellView?.identifier = cellIdentifier

            // Create a simple, standard text field
            let textField = NSTextField()
            textField.isEditable = true
            textField.isSelectable = true
            textField.isBordered = false
            textField.backgroundColor = NSColor.clear
            textField.textColor = NSColor.labelColor
            textField.translatesAutoresizingMaskIntoConstraints = false
            textField.font = NSFont.systemFont(ofSize: 18)
            textField.alignment = .left

            // Set delegate to capture editing events
            textField.delegate = self
            textField.target = self
            textField.action = #selector(textFieldEditingFinished(_:))

            cellView?.addSubview(textField)
            cellView?.textField = textField

            // Set up constraints
            NSLayoutConstraint.activate([
                textField.leadingAnchor.constraint(equalTo: cellView!.leadingAnchor, constant: 4),
                textField.trailingAnchor.constraint(equalTo: cellView!.trailingAnchor, constant: -4),
                textField.topAnchor.constraint(equalTo: cellView!.topAnchor, constant: 2),
                textField.bottomAnchor.constraint(equalTo: cellView!.bottomAnchor, constant: -2)
            ])
        }

        // Set the value
        cellView?.textField?.stringValue = tableData[row][columnIndex]

        // Ensure delegate is set for reused cells
        cellView?.textField?.delegate = self
        cellView?.textField?.target = self
        cellView?.textField?.action = #selector(textFieldEditingFinished(_:))

        return cellView
    }

    @objc private func textFieldEditingFinished(_ sender: NSTextField) {
        // Find which cell this text field belongs to
        guard let cellView = sender.superview as? NSTableCellView else {
            return
        }

        let row = tableView.row(for: cellView)
        let column = tableView.column(for: cellView)

        guard row >= 0 && row < tableData.count,
              column >= 0 && column < tableData[row].count else {
            return
        }

        // Update the table data
        tableData[row][column] = sender.stringValue

        // Save changes immediately
        saveChangesToResources()
    }

    func checkForDataChanges() {
        // Check if any text fields have different values than our table data
        for row in 0..<tableView.numberOfRows {
            for column in 0..<tableView.numberOfColumns {
                if let cellView = tableView.view(atColumn: column, row: row, makeIfNecessary: false) as? NSTableCellView,
                   let textField = cellView.textField {
                    let currentValue = textField.stringValue
                    let storedValue = (row < tableData.count && column < tableData[row].count) ? tableData[row][column] : ""

                    if currentValue != storedValue {
                        // Update our data
                        if row < tableData.count && column < tableData[row].count {
                            tableData[row][column] = currentValue
                            saveChangesToResources()
                        }
                    }
                }
            }
        }
    }

    func saveChangesToResources() {
        // Create new resources with edited data
        var updatedResources: [ProcessedResourceWithExtras] = []

        for (index, resource) in resources.enumerated() {
            if index < tableData.count {
                let row = tableData[index]
                if row.count >= columnHeaders.count - 1 { // -1 because QR code column is not editable data
                    // Create new resource with edited values
                    let updatedResource = ProcessedResourceWithExtras(
                        resourceId: row[6],  // resourceID*
                        resourceLink: row[7],  // res_link
                        title: row[0],  // Title*
                        priority: Int(row[5]) ?? resource.priority,  // Pri
                        resdefId: row[4].isEmpty ? nil : row[4],  // resdef*
                        location: row[9].isEmpty ? nil : row[9],  // location
                        found: resource.found,  // Keep original found status
                        labelTitle: row[1],  // Label Title
                        speed: row[3]  // speed
                    )

                    updatedResources.append(updatedResource)
                } else {
                    // Keep original resource if row data is incomplete
                    updatedResources.append(resource)
                }
            } else {
                // Keep original resource if no corresponding row data
                updatedResources.append(resource)
            }
        }

        // Update the stored resources
        resources = updatedResources

        // Call the callback to notify that data has changed
        onDataChanged?(resources)
    }
    
    @objc private func cellTextChanged(_ sender: NSTextField) {
        guard let cellView = sender.superview as? NSTableCellView else {
            return
        }

        let row = tableView.row(for: cellView)
        let column = tableView.column(for: cellView)

        guard row >= 0 && row < tableData.count,
              column >= 0 && column < tableData[row].count else {
            return
        }

        tableData[row][column] = sender.stringValue

        // Immediately save changes to resources when data is edited
        saveChangesToResources()
    }

    // MARK: - NSTableView Editing Support

    func tableView(_ tableView: NSTableView, setObjectValue object: Any?, for tableColumn: NSTableColumn?, row: Int) {
        guard let column = tableColumn,
              let columnIndex = tableView.tableColumns.firstIndex(of: column),
              let newValue = object as? String,
              row < tableData.count,
              columnIndex < tableData[row].count else {
            return
        }

        // Update the table data
        tableData[row][columnIndex] = newValue

        // Save changes immediately
        saveChangesToResources()
    }

    func tableView(_ tableView: NSTableView, objectValueFor tableColumn: NSTableColumn?, row: Int) -> Any? {
        guard let column = tableColumn,
              let columnIndex = tableView.tableColumns.firstIndex(of: column),
              row < tableData.count,
              columnIndex < tableData[row].count else {
            return nil
        }

        let value = tableData[row][columnIndex]
        return value
    }

    // Add this method to detect when editing begins
    func tableView(_ tableView: NSTableView, shouldEdit tableColumn: NSTableColumn?, row: Int) -> Bool {
        return true
    }

    // MARK: - NSTextFieldDelegate

    func controlTextDidEndEditing(_ obj: Notification) {
        guard let textField = obj.object as? NSTextField else {
            return
        }

        textFieldEditingFinished(textField)
    }

    func controlTextDidChange(_ obj: Notification) {
        guard obj.object is NSTextField else {
            return
        }

        // For real-time updates, we could call textFieldEditingFinished here too
        // But for now, we don't need to do anything
    }
}

// MARK: - NSWindowDelegate

extension ResourcePreviewWindowController: NSWindowDelegate {

    func windowShouldClose(_ sender: NSWindow) -> Bool {
        // Before closing, check for any unsaved changes
        checkForDataChanges()
        saveChangesToResources()

        return true // Allow the window to close
    }

    func windowWillClose(_ notification: Notification) {
        // Notify that window is closing so ViewController can clear the reference
        onWindowClosing?()
    }

    // MARK: - QR Code Generation

    private func generateQRCode(from string: String) -> NSImage? {
        let data = string.data(using: String.Encoding.ascii)

        if let filter = CIFilter(name: "CIQRCodeGenerator") {
            filter.setValue(data, forKey: "inputMessage")
            let transform = CGAffineTransform(scaleX: 3, y: 3)

            if let output = filter.outputImage?.transformed(by: transform) {
                let rep = NSCIImageRep(ciImage: output)
                let nsImage = NSImage(size: rep.size)
                nsImage.addRepresentation(rep)
                return nsImage
            }
        }

        return nil
    }

    private func createQRCodeImageView(for link: String) -> NSImageView {
        let imageView = NSImageView()
        imageView.imageScaling = .scaleProportionallyUpOrDown
        imageView.imageAlignment = .alignCenter

        if let qrImage = generateQRCode(from: link) {
            imageView.image = qrImage
        } else {
            // Create a placeholder image if QR generation fails
            let placeholderImage = NSImage(systemSymbolName: "qrcode", accessibilityDescription: "QR Code")
            imageView.image = placeholderImage
        }

        return imageView
    }
}
