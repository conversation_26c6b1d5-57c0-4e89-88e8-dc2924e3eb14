import Foundation

class ResourceManagerConfig: ObservableObject {
    static let shared = ResourceManagerConfig()
    
    private let userDefaults = UserDefaults.standard
    
    // MARK: - Configuration Keys
    private enum Keys {
        static let componentId = "ResourceManager.ComponentId"
        static let driId = "ResourceManager.DriId"
        static let inventoryKeeperId = "ResourceManager.InventoryKeeperId"
        static let locationId = "ResourceManager.LocationId"
        static let specificLocation = "ResourceManager.SpecificLocation"
        static let classId = "ResourceManager.ClassId"
        static let stateId = "ResourceManager.StateId"
    }
    
    // MARK: - Default Values (fallback values)
    private enum DefaultValues {
        static let componentId = 1118899
        static let driId = 973776146
        static let inventoryKeeperId = 973776146
        static let locationId = 66842
        static let specificLocation = "Aruba"
        static let classId = 1
        static let stateId = 1
    }
    
    // MARK: - Published Properties
    @Published var componentId: Int {
        didSet {
            userDefaults.set(componentId, forKey: Keys.componentId)
        }
    }
    
    @Published var driId: Int {
        didSet {
            userDefaults.set(driId, forKey: Keys.driId)
        }
    }
    
    @Published var inventoryKeeperId: Int {
        didSet {
            userDefaults.set(inventoryKeeperId, forKey: Keys.inventoryKeeperId)
        }
    }
    
    @Published var locationId: Int {
        didSet {
            userDefaults.set(locationId, forKey: Keys.locationId)
        }
    }
    
    @Published var specificLocation: String {
        didSet {
            userDefaults.set(specificLocation, forKey: Keys.specificLocation)
        }
    }
    
    @Published var classId: Int {
        didSet {
            userDefaults.set(classId, forKey: Keys.classId)
        }
    }
    
    @Published var stateId: Int {
        didSet {
            userDefaults.set(stateId, forKey: Keys.stateId)
        }
    }
    
    // MARK: - Initialization
    private init() {
        // Load values from UserDefaults or use default values
        self.componentId = userDefaults.object(forKey: Keys.componentId) as? Int ?? DefaultValues.componentId
        self.driId = userDefaults.object(forKey: Keys.driId) as? Int ?? DefaultValues.driId
        self.inventoryKeeperId = userDefaults.object(forKey: Keys.inventoryKeeperId) as? Int ?? DefaultValues.inventoryKeeperId
        self.locationId = userDefaults.object(forKey: Keys.locationId) as? Int ?? DefaultValues.locationId
        self.specificLocation = userDefaults.object(forKey: Keys.specificLocation) as? String ?? DefaultValues.specificLocation
        self.classId = userDefaults.object(forKey: Keys.classId) as? Int ?? DefaultValues.classId
        self.stateId = userDefaults.object(forKey: Keys.stateId) as? Int ?? DefaultValues.stateId
    }
    
    // MARK: - Configuration Management
    func saveConfiguration() {
        userDefaults.synchronize()
        // Level 3: Debug logging - configuration save details
        print("🔧 Configuration saved:")
        print("   Component ID: \(componentId)")
        print("   DRI ID: \(driId)")
        print("   Inventory Keeper ID: \(inventoryKeeperId)")
        print("   Location ID: \(locationId)")
        print("   Specific Location: \(specificLocation)")
        print("   Class ID: \(classId)")
        print("   State ID: \(stateId)")
    }
    
    func resetToDefaults() {
        componentId = DefaultValues.componentId
        driId = DefaultValues.driId
        inventoryKeeperId = DefaultValues.inventoryKeeperId
        locationId = DefaultValues.locationId
        specificLocation = DefaultValues.specificLocation
        classId = DefaultValues.classId
        stateId = DefaultValues.stateId
        saveConfiguration()
        // Level 3: Debug logging - configuration reset
        print("🔧 Configuration reset to default values")
    }
    
    func exportConfiguration() -> [String: Any] {
        return [
            "componentId": componentId,
            "driId": driId,
            "inventoryKeeperId": inventoryKeeperId,
            "locationId": locationId,
            "specificLocation": specificLocation,
            "classId": classId,
            "stateId": stateId
        ]
    }
    
    func importConfiguration(from config: [String: Any]) {
        if let value = config["componentId"] as? Int {
            componentId = value
        }
        if let value = config["driId"] as? Int {
            driId = value
        }
        if let value = config["inventoryKeeperId"] as? Int {
            inventoryKeeperId = value
        }
        if let value = config["locationId"] as? Int {
            locationId = value
        }
        if let value = config["specificLocation"] as? String {
            specificLocation = value
        }
        if let value = config["classId"] as? Int {
            classId = value
        }
        if let value = config["stateId"] as? Int {
            stateId = value
        }
        saveConfiguration()
        // Level 3: Debug logging - configuration import
        print("🔧 Configuration imported successfully")
    }
}
