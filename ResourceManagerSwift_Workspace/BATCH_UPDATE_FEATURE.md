# Batch Resource Update Feature

## Overview
The Resource Manager now supports batch resource updates through the single resource update interface. Users can input multiple resource IDs in a single operation.

## Supported Input Formats

### Single Resource ID
- `286044`
- `rdar://res/286044`
- `  286044  ` (with whitespace)

### Batch Resource IDs
- `rdar://res/286044&286046&286048&286050`
- `286044&286046&286048&286050`
- `  286044  &  286046  &  286048  &  286050  ` (with whitespace)

## Features

### Input Validation
- Supports resource IDs with 4-8 digits
- Automatically removes `rdar://res/` prefix
- Trims whitespace from individual IDs
- Skips invalid IDs and continues processing valid ones

### Batch Processing
- Processes each resource ID sequentially
- Applies the same title, priority, and location to all resources
- Continues processing even if some resources fail
- Provides detailed logging for each resource

### Error Handling
- Individual error reporting for each failed resource
- Kerberos authentication error detection
- Comprehensive success/failure reporting

### Logging
- Different log messages for single vs batch operations
- Individual resource link reporting
- Combined success reporting

## Usage Example

1. Enter resource IDs in the Resource ID field:
   ```
   rdar://res/286044&286046&286048&286050
   ```

2. Fill in other fields (Title, Priority, Location) as needed

3. Click "Update Single Resource" button

4. The system will:
   - Parse the input to extract individual resource IDs: [286044, 286046, 286048, 286050]
   - Update each resource with the provided information
   - Display progress and results in the log
   - Show a preview window with all updated resources

## Implementation Details

### parseResourceIds Function
- Handles both single and batch formats
- Validates resource ID length (4-8 digits)
- Returns array of valid integer resource IDs

### Batch Update Logic
- Iterates through each parsed resource ID
- Calls existing `updateSingleResource` API for each ID
- Collects all successful results
- Provides comprehensive error handling

### UI Integration
- Uses existing single resource update interface
- No UI changes required
- Maintains backward compatibility with single resource updates
