## RadarFetch MCP Server: Developer Guide and Examples

This guide explains how to use the RadarFetch MCP server functions and shows example calls for development and testing.

---

### 1) Environment and Auth Strategy
- The server determines auth strategy at import time:
  - OAuth if both RADAR_OIDC_CLIENT_ID and RADAR_OIDC_CLIENT_SECRET are set
  - Otherwise, falls back to AppleConnect
- The global constant `AUTH_STRATEGY` reflects the computed strategy.

Example (CLI health):
```
python3 -m mcp.radarfetch_server
# Output example:
# RadarFetch MCP scaffold loaded; auth_strategy=appleconnect; fastmcp=missing; mcp_instance=not_created; client_ready=yes
```

---

### 2) Using without FastMCP (direct Python usage)
All tool functions are available as regular Python functions, even if FastMCP is not installed.

Example:
```
from local_mcp.radarfetch_server import (
    get_resource_by_id,
    create_resource,
    update_resource,
    search_resource_definitions,
    get_resource_definition_by_id,
    get_resource_fields,
    update_resource_fields,
    generate_print_links,
)

print(get_resource_by_id(123456))
print(create_resource(title="MyTitle", resdef_id=987, priority=3, quantity=1, location="Lab"))
# Returns: "Created resource: rdar://res/123456"

print(create_resource(title="ATC USB3 - TBT3_40 27776", quantity=2))  # Extract resdef from title
# Returns: "Created 2 resources:\nCombined: rdar://res/123456,123457\nIndividual: rdar://res/123456, rdar://res/123457"

print(create_resource(resdef_id=27776, quantity=1))  # Use resdef title and priority
# Returns: "Created resource: rdar://res/123458"

print(update_resource(123456, title="NewTitle", priority=4))
# Returns: "Updated resource: rdar://res/123456"

print(attach_resource_to_resdef(123456, 27776))  # Attach existing resource to resdef
# Returns: "Resource rdar://res/123456 (Resource Title) attached to resdef 27776"
print(search_resource_definitions("USB"))
print(get_resource_definition_by_id(987))
print(get_resource_fields(123456, ["title", "priority"]))
print(update_resource_fields(123456, {"priority": 3}))
print(generate_print_links([123456, 123457]))
```

---

### 3) Using with FastMCP
- If `fastmcp` is installed, an MCP server instance is created as `mcp = FastMCP("RadarFetch")` and each tool is registered using a decorator shim.
- A host that understands MCP can discover tools (names, parameters, and docstrings) directly from this module.
- A full MCP bridge/run loop is not included here; integrate with your MCP host according to its instructions.

---

### 4) Testing without network (monkeypatch)
You can monkeypatch `radarclient` methods to avoid network calls during unit tests:
```
from local_mcp import radarfetch_server as r

# Fake implementations
r.radarclient.resource_for_id = lambda rid, timeout=60: {"id": rid, "title": f"Resource-{rid}"}

class FakeResp:
    def __init__(self, rid):
        self._rid = rid
    def json(self):
        return {"id": self._rid}

_created = []

def fake_create(payload, timeout=60):
    rid = 1000 + len(_created)
    _created.append(rid)
    return FakeResp(rid)

r.radarclient.create_resource = fake_create
r.radarclient.update_resource = lambda rid, payload, timeout=60: {"id": rid, "updated": list(payload.keys())}

print(r.get_resource_by_id(42))
print(r.create_resource(5, "Foo", priority=3, quantity=2, location="Lab"))
print(r.update_resource(42, title="Bar", priority=2))
```

Expected output (example):
```
Resource 42: title='Resource-42'
Created 2 resources: rdar://res/1000,1001
Updated resource 42: rdar://res/42; fields=['title', 'priority']
```

---

### 5) Tool function reference (summary)
- get_resource_by_id(radar_id: int) -> str
- create_resource(title: str = "", resdef_id: int = 0, priority: int = -1, quantity: int = 1, location: str = "") -> str
  * At least one of title or resdef_id must be provided
  * If resdef_id provided: uses resdef title and priority (unless overridden)
  * If only title provided: extracts resdef_id from title (format: "Title - Speed ResdefID")
  * Priority: user input > resdef keywords > default (5)
  * Automatically attaches created resources to the specified resdef using attach_resource_to_resdef()
  * Returns: rdar:// links for created resources (individual for single, combined + individual for multiple)
- update_resource(resource_id: int, title: str = "", priority: int = -1, specific_location: str = "") -> str
  * Returns: rdar:// link for updated resource
- attach_resource_to_resdef(resource_id: int, resdef_id: int) -> str
  * Attach an existing resource to a resource definition
  * Equivalent to Python res2resdef.py functionality
  * Handles "already attached" case gracefully
  * Returns: rdar:// link with attachment confirmation
- search_resource_definitions(title_substr: str) -> str
- get_resource_definition_by_id(resdef_id: int) -> str
- get_resource_fields(resource_id: int, fields: list[str]) -> str
- update_resource_fields(resource_id: int, fields: dict[str, object]) -> str
- generate_print_links(resource_ids: list[int]) -> str

Notes:
- All functions return human-readable strings. Errors are prefixed with "Error:".
- Field names and payloads must match the Radar API expectations.

