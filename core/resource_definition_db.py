"""
Resource Definition Database Manager.

This module provides database operations for local resource definition storage,
including CRUD operations, search functionality, and connection management.
"""

import sqlite3
import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from contextlib import contextmanager

from config import DatabaseConfig
from core.database_models import ResourceDefinition, DatabaseSchema, TitleParser


class ResourceDefinitionDB:
    """Database manager for resource definitions."""
    
    def __init__(self, db_path: Optional[Path] = None):
        """Initialize database manager."""
        self.db_path = db_path or DatabaseConfig.get_database_path()
        self.logger = logging.getLogger(__name__)
        self._ensure_database_exists()
    
    def _ensure_database_exists(self) -> None:
        """Ensure database file and tables exist."""
        # Create directory if it doesn't exist
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create tables if they don't exist
        with self.get_connection() as conn:
            DatabaseSchema.create_all_tables(conn)
            
            # Enable WAL mode for better performance
            if DatabaseConfig.ENABLE_WAL_MODE:
                conn.execute("PRAGMA journal_mode=WAL;")
            
            self.logger.info(f"Database initialized at {self.db_path}")
    
    @contextmanager
    def get_connection(self):
        """Get database connection with proper error handling."""
        conn = None
        try:
            conn = sqlite3.connect(
                str(self.db_path),
                timeout=DatabaseConfig.CONNECTION_TIMEOUT
            )
            conn.row_factory = sqlite3.Row  # Enable column access by name
            yield conn
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def insert_resource_definition(self, resdef: ResourceDefinition) -> bool:
        """Insert a single resource definition."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Set timestamps
                now = datetime.now().isoformat()
                resdef.created_at = now
                resdef.updated_at = now
                
                # Insert into main table
                cursor.execute("""
                    INSERT OR REPLACE INTO resource_definitions 
                    (resdef_id, title, priority, store_sku, manufacturer, product_name, 
                     model_number, chipsets, technologies, comments, interface, 
                     keywords, device_attributes, category, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    resdef.resdef_id, resdef.title, resdef.priority, resdef.store_sku,
                    resdef.manufacturer, resdef.product_name, resdef.model_number,
                    resdef.chipsets, resdef.technologies, resdef.comments,
                    resdef.interface, resdef.keywords, resdef.device_attributes,
                    resdef.category, resdef.created_at, resdef.updated_at
                ))
                
                conn.commit()
                self.logger.debug(f"Inserted resource definition: {resdef.resdef_id}")
                return True
                
        except sqlite3.Error as e:
            self.logger.error(f"Error inserting resource definition {resdef.resdef_id}: {e}")
            return False
    
    def insert_batch(self, resdefs: List[ResourceDefinition]) -> Tuple[int, int]:
        """Insert multiple resource definitions in batch."""
        success_count = 0
        error_count = 0
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()
                
                # Prepare batch data
                batch_data = []
                for resdef in resdefs:
                    resdef.created_at = now
                    resdef.updated_at = now
                    batch_data.append((
                        resdef.resdef_id, resdef.title, resdef.priority, resdef.store_sku,
                        resdef.manufacturer, resdef.product_name, resdef.model_number,
                        resdef.chipsets, resdef.technologies, resdef.comments,
                        resdef.interface, resdef.keywords, resdef.device_attributes,
                        resdef.category, resdef.created_at, resdef.updated_at
                    ))
                
                # Execute batch insert
                cursor.executemany("""
                    INSERT OR REPLACE INTO resource_definitions 
                    (resdef_id, title, priority, store_sku, manufacturer, product_name, 
                     model_number, chipsets, technologies, comments, interface, 
                     keywords, device_attributes, category, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, batch_data)
                
                conn.commit()
                success_count = len(batch_data)
                self.logger.info(f"Batch inserted {success_count} resource definitions")
                
        except sqlite3.Error as e:
            self.logger.error(f"Error in batch insert: {e}")
            error_count = len(resdefs)
        
        return success_count, error_count
    
    def get_by_id(self, resdef_id: str) -> Optional[ResourceDefinition]:
        """Get resource definition by ID."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM resource_definitions WHERE resdef_id = ?
                """, (resdef_id,))
                
                row = cursor.fetchone()
                if row:
                    return ResourceDefinition.from_dict(dict(row))
                return None
                
        except sqlite3.Error as e:
            self.logger.error(f"Error getting resource definition {resdef_id}: {e}")
            return None
    
    def search_by_keywords(self, query: str, limit: int = 50) -> List[ResourceDefinition]:
        """Search resource definitions by keywords using FTS."""
        if not DatabaseConfig.ENABLE_FTS:
            return self._search_by_keywords_basic(query, limit)
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Use FTS5 MATCH for full-text search
                cursor.execute("""
                    SELECT rd.* FROM resource_definitions rd
                    JOIN resource_definitions_fts fts ON rd.resdef_id = fts.resdef_id
                    WHERE resource_definitions_fts MATCH ?
                    ORDER BY rank
                    LIMIT ?
                """, (query, limit))
                
                results = []
                for row in cursor.fetchall():
                    results.append(ResourceDefinition.from_dict(dict(row)))
                
                self.logger.debug(f"FTS search for '{query}' returned {len(results)} results")
                return results
                
        except sqlite3.Error as e:
            self.logger.error(f"Error in FTS search for '{query}': {e}")
            # Fallback to basic search
            return self._search_by_keywords_basic(query, limit)
    
    def _search_by_keywords_basic(self, query: str, limit: int = 50) -> List[ResourceDefinition]:
        """Basic keyword search without FTS."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Split query into terms and search in multiple fields
                terms = query.lower().split()
                where_conditions = []
                params = []
                
                for term in terms:
                    term_pattern = f"%{term}%"
                    where_conditions.append("""
                        (LOWER(title) LIKE ? OR 
                         LOWER(manufacturer) LIKE ? OR 
                         LOWER(product_name) LIKE ? OR 
                         LOWER(keywords) LIKE ?)
                    """)
                    params.extend([term_pattern] * 4)
                
                where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
                
                cursor.execute(f"""
                    SELECT * FROM resource_definitions 
                    WHERE {where_clause}
                    ORDER BY title
                    LIMIT ?
                """, params + [limit])
                
                results = []
                for row in cursor.fetchall():
                    results.append(ResourceDefinition.from_dict(dict(row)))
                
                self.logger.debug(f"Basic search for '{query}' returned {len(results)} results")
                return results
                
        except sqlite3.Error as e:
            self.logger.error(f"Error in basic search for '{query}': {e}")
            return []
    
    def search_by_manufacturer(self, manufacturer: str, limit: int = 50) -> List[ResourceDefinition]:
        """Search resource definitions by manufacturer."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM resource_definitions 
                    WHERE LOWER(manufacturer) LIKE LOWER(?)
                    ORDER BY title
                    LIMIT ?
                """, (f"%{manufacturer}%", limit))
                
                results = []
                for row in cursor.fetchall():
                    results.append(ResourceDefinition.from_dict(dict(row)))
                
                return results
                
        except sqlite3.Error as e:
            self.logger.error(f"Error searching by manufacturer '{manufacturer}': {e}")
            return []
    
    def search_by_category(self, category: str, limit: int = 50) -> List[ResourceDefinition]:
        """Search resource definitions by category."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM resource_definitions 
                    WHERE category = ?
                    ORDER BY title
                    LIMIT ?
                """, (category, limit))
                
                results = []
                for row in cursor.fetchall():
                    results.append(ResourceDefinition.from_dict(dict(row)))
                
                return results
                
        except sqlite3.Error as e:
            self.logger.error(f"Error searching by category '{category}': {e}")
            return []
    
    def get_all_manufacturers(self) -> List[str]:
        """Get list of all unique manufacturers."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT DISTINCT manufacturer FROM resource_definitions 
                    WHERE manufacturer IS NOT NULL 
                    ORDER BY manufacturer
                """)
                
                return [row[0] for row in cursor.fetchall()]
                
        except sqlite3.Error as e:
            self.logger.error(f"Error getting manufacturers: {e}")
            return []
    
    def get_all_categories(self) -> List[str]:
        """Get list of all unique categories."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT DISTINCT category FROM resource_definitions 
                    WHERE category IS NOT NULL 
                    ORDER BY category
                """)
                
                return [row[0] for row in cursor.fetchall()]
                
        except sqlite3.Error as e:
            self.logger.error(f"Error getting categories: {e}")
            return []
    
    def get_count(self) -> int:
        """Get total count of resource definitions."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM resource_definitions")
                return cursor.fetchone()[0]
                
        except sqlite3.Error as e:
            self.logger.error(f"Error getting count: {e}")
            return 0
    
    def delete_by_id(self, resdef_id: str) -> bool:
        """Delete resource definition by ID."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM resource_definitions WHERE resdef_id = ?", (resdef_id,))
                conn.commit()
                
                deleted = cursor.rowcount > 0
                if deleted:
                    self.logger.debug(f"Deleted resource definition: {resdef_id}")
                return deleted
                
        except sqlite3.Error as e:
            self.logger.error(f"Error deleting resource definition {resdef_id}: {e}")
            return False
    
    def clear_all(self) -> bool:
        """Clear all resource definitions from database."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM resource_definitions")
                conn.commit()
                
                self.logger.info("Cleared all resource definitions from database")
                return True
                
        except sqlite3.Error as e:
            self.logger.error(f"Error clearing database: {e}")
            return False
