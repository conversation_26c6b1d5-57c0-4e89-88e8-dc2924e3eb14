"""
CSV Import functionality for Resource Definitions.

This module provides functionality to import resource definitions from CSV files,
parse titles according to Resource Definition.md rules, and populate the database.
"""

import csv
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

from core.database_models import ResourceDefinition, TitleParser
from core.resource_definition_db import ResourceDefinitionDB
from config import DatabaseConfig


@dataclass
class ImportResult:
    """Result of CSV import operation."""
    total_rows: int
    successful_imports: int
    failed_imports: int
    errors: List[str]
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        if self.total_rows == 0:
            return 0.0
        return (self.successful_imports / self.total_rows) * 100


class CSVImporter:
    """CSV importer for resource definitions."""
    
    def __init__(self, db_manager: Optional[ResourceDefinitionDB] = None):
        """Initialize CSV importer."""
        self.db_manager = db_manager or ResourceDefinitionDB()
        self.logger = logging.getLogger(__name__)
    
    def import_from_csv(self, csv_path: Path, 
                       resdef_id_column: str = 'resourcedefitionID',
                       title_column: str = 'Title', 
                       priority_column: str = 'Priority') -> ImportResult:
        """
        Import resource definitions from CSV file.
        
        Args:
            csv_path: Path to CSV file
            resdef_id_column: Name of column containing resource definition IDs
            title_column: Name of column containing titles
            priority_column: Name of column containing priorities
            
        Returns:
            ImportResult with statistics and errors
        """
        errors = []
        resource_definitions = []
        
        try:
            # Read CSV file
            with open(csv_path, 'r', encoding='utf-8') as file:
                # Detect delimiter
                sample = file.read(1024)
                file.seek(0)
                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter
                
                reader = csv.DictReader(file, delimiter=delimiter)
                
                # Validate required columns
                if not self._validate_columns(reader.fieldnames, 
                                             [resdef_id_column, title_column], 
                                             errors):
                    return ImportResult(0, 0, 0, errors)
                
                # Process each row
                for row_num, row in enumerate(reader, start=2):  # Start at 2 for header
                    try:
                        resdef = self._process_row(row, resdef_id_column, 
                                                 title_column, priority_column)
                        if resdef:
                            resource_definitions.append(resdef)
                        else:
                            errors.append(f"Row {row_num}: Failed to process row")
                            
                    except Exception as e:
                        errors.append(f"Row {row_num}: {str(e)}")
                        self.logger.error(f"Error processing row {row_num}: {e}")
        
        except FileNotFoundError:
            errors.append(f"CSV file not found: {csv_path}")
            return ImportResult(0, 0, 0, errors)
        except Exception as e:
            errors.append(f"Error reading CSV file: {str(e)}")
            return ImportResult(0, 0, 0, errors)
        
        # Import to database in batches
        total_rows = len(resource_definitions)
        if total_rows == 0:
            return ImportResult(0, 0, 0, errors)
        
        successful_imports, failed_imports = self._import_batch(resource_definitions)
        
        # Log results
        self.logger.info(f"CSV import completed: {successful_imports}/{total_rows} successful")
        if errors:
            self.logger.warning(f"Import had {len(errors)} errors")
        
        return ImportResult(
            total_rows=total_rows,
            successful_imports=successful_imports,
            failed_imports=failed_imports,
            errors=errors
        )
    
    def _validate_columns(self, fieldnames: List[str], required_columns: List[str], 
                         errors: List[str]) -> bool:
        """Validate that required columns exist in CSV."""
        if not fieldnames:
            errors.append("CSV file has no columns")
            return False
        
        missing_columns = []
        for col in required_columns:
            if col not in fieldnames:
                missing_columns.append(col)
        
        if missing_columns:
            errors.append(f"Missing required columns: {', '.join(missing_columns)}")
            errors.append(f"Available columns: {', '.join(fieldnames)}")
            return False
        
        return True
    
    def _process_row(self, row: Dict[str, str], resdef_id_column: str, 
                    title_column: str, priority_column: str) -> Optional[ResourceDefinition]:
        """Process a single CSV row into ResourceDefinition."""
        # Extract basic fields
        resdef_id = row.get(resdef_id_column, '').strip()
        title = row.get(title_column, '').strip()
        priority_str = row.get(priority_column, '').strip()
        
        # Validate required fields
        if not resdef_id:
            raise ValueError("Resource definition ID is required")
        if not title:
            raise ValueError("Title is required")
        
        # Parse priority
        priority = None
        if priority_str:
            try:
                priority = int(priority_str)
                # Validate priority range
                if not (DatabaseConfig.PRIORITY_MIN <= priority <= DatabaseConfig.PRIORITY_MAX):
                    self.logger.warning(f"Priority {priority} out of range for {resdef_id}")
            except ValueError:
                self.logger.warning(f"Invalid priority '{priority_str}' for {resdef_id}")
        
        # Parse title components
        parsed_components = TitleParser.parse_title(title)
        
        # Create ResourceDefinition
        resdef = ResourceDefinition(
            resdef_id=resdef_id,
            title=title,
            priority=priority,
            store_sku=parsed_components.get('store_sku'),
            manufacturer=parsed_components.get('manufacturer'),
            product_name=parsed_components.get('product_name'),
            model_number=parsed_components.get('model_number'),
            chipsets=parsed_components.get('chipsets'),
            technologies=parsed_components.get('technologies'),
            comments=parsed_components.get('comments'),
            interface=parsed_components.get('interface'),
            keywords=parsed_components.get('keywords'),
            category=parsed_components.get('category')
        )
        
        return resdef
    
    def _import_batch(self, resource_definitions: List[ResourceDefinition]) -> Tuple[int, int]:
        """Import resource definitions to database in batches."""
        total_successful = 0
        total_failed = 0
        
        # Process in batches for better performance
        batch_size = DatabaseConfig.BATCH_SIZE
        for i in range(0, len(resource_definitions), batch_size):
            batch = resource_definitions[i:i + batch_size]
            successful, failed = self.db_manager.insert_batch(batch)
            total_successful += successful
            total_failed += failed
            
            self.logger.debug(f"Processed batch {i//batch_size + 1}: "
                            f"{successful} successful, {failed} failed")
        
        return total_successful, total_failed
    
    def import_sample_data(self) -> ImportResult:
        """Import sample data for testing purposes."""
        sample_data = [
            {
                'resdef_id': '11859',
                'title': '(Store HLLF2VC/A) G-TECH G-RAID with Thunderbolt 3 8TB (0G05776/0G05750) [AR][TPS65983][ASM1062][ASM1092R][ASM235CM]{UAS} - USB-C/TBT3_40/USB3_5',
                'priority': 2
            },
            {
                'resdef_id': '12306',
                'title': '(Store HMUB2LL/A) LG UltraFine 5K Display (27MD5KA-B.AUSA)(P112){USB-PD 94W}[GL3523] - USB-C/TBT3_40',
                'priority': 1
            },
            {
                'resdef_id': '12345',
                'title': 'Apple USB-C to Lightning Cable (1m) - USB-C/Lightning',
                'priority': 3
            }
        ]
        
        resource_definitions = []
        errors = []
        
        for data in sample_data:
            try:
                # Parse title components
                parsed_components = TitleParser.parse_title(data['title'])
                
                # Create ResourceDefinition
                resdef = ResourceDefinition(
                    resdef_id=data['resdef_id'],
                    title=data['title'],
                    priority=data['priority'],
                    store_sku=parsed_components.get('store_sku'),
                    manufacturer=parsed_components.get('manufacturer'),
                    product_name=parsed_components.get('product_name'),
                    model_number=parsed_components.get('model_number'),
                    chipsets=parsed_components.get('chipsets'),
                    technologies=parsed_components.get('technologies'),
                    comments=parsed_components.get('comments'),
                    interface=parsed_components.get('interface'),
                    keywords=parsed_components.get('keywords'),
                    category=parsed_components.get('category')
                )
                
                resource_definitions.append(resdef)
                
            except Exception as e:
                errors.append(f"Error processing sample data {data['resdef_id']}: {str(e)}")
        
        # Import to database
        successful_imports, failed_imports = self._import_batch(resource_definitions)
        
        self.logger.info(f"Sample data import: {successful_imports}/{len(sample_data)} successful")
        
        return ImportResult(
            total_rows=len(sample_data),
            successful_imports=successful_imports,
            failed_imports=failed_imports,
            errors=errors
        )
    
    def validate_csv_format(self, csv_path: Path, 
                           resdef_id_column: str = 'resourcedefitionID',
                           title_column: str = 'Title', 
                           priority_column: str = 'Priority') -> Tuple[bool, List[str]]:
        """
        Validate CSV file format without importing.
        
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        try:
            with open(csv_path, 'r', encoding='utf-8') as file:
                # Check if file is empty
                if file.read(1) == '':
                    errors.append("CSV file is empty")
                    return False, errors
                
                file.seek(0)
                
                # Detect delimiter and read header
                sample = file.read(1024)
                file.seek(0)
                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter
                
                reader = csv.DictReader(file, delimiter=delimiter)
                
                # Validate columns
                if not self._validate_columns(reader.fieldnames, 
                                             [resdef_id_column, title_column], 
                                             errors):
                    return False, errors
                
                # Check first few rows for basic validation
                row_count = 0
                for row_num, row in enumerate(reader, start=2):
                    if row_num > 5:  # Only check first 5 rows for validation
                        break
                    
                    row_count += 1
                    resdef_id = row.get(resdef_id_column, '').strip()
                    title = row.get(title_column, '').strip()
                    
                    if not resdef_id:
                        errors.append(f"Row {row_num}: Missing resource definition ID")
                    if not title:
                        errors.append(f"Row {row_num}: Missing title")
                
                if row_count == 0:
                    errors.append("CSV file has no data rows")
                    return False, errors
        
        except FileNotFoundError:
            errors.append(f"CSV file not found: {csv_path}")
            return False, errors
        except Exception as e:
            errors.append(f"Error validating CSV file: {str(e)}")
            return False, errors
        
        is_valid = len(errors) == 0
        return is_valid, errors
