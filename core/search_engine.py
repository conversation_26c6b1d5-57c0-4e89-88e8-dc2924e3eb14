"""
Search Engine for Resource Definitions.

This module provides advanced search functionality for finding resource definitions
by keywords, manufacturer, product name, technologies, and other criteria.
"""

import re
import logging
from typing import List, Dict, Any, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum

from core.database_models import ResourceDefinition
from core.resource_definition_db import ResourceDefinitionDB


class SearchType(Enum):
    """Types of search operations."""
    KEYWORD = "keyword"
    MANUFACTURER = "manufacturer"
    CATEGORY = "category"
    INTERFACE = "interface"
    TECHNOLOGY = "technology"
    CHIPSET = "chipset"
    PRIORITY = "priority"
    COMBINED = "combined"


@dataclass
class SearchFilter:
    """Search filter criteria."""
    manufacturer: Optional[str] = None
    category: Optional[str] = None
    interface: Optional[str] = None
    priority_min: Optional[int] = None
    priority_max: Optional[int] = None
    has_store_sku: Optional[bool] = None


@dataclass
class SearchResult:
    """Search result with relevance scoring."""
    resource_definition: ResourceDefinition
    relevance_score: float
    matched_fields: List[str]
    
    def __lt__(self, other):
        """Enable sorting by relevance score."""
        return self.relevance_score > other.relevance_score  # Higher score first


class SearchEngine:
    """Advanced search engine for resource definitions."""
    
    def __init__(self, db_manager: Optional[ResourceDefinitionDB] = None):
        """Initialize search engine."""
        self.db_manager = db_manager or ResourceDefinitionDB()
        self.logger = logging.getLogger(__name__)
    
    def search(self, query: str, 
               search_type: SearchType = SearchType.KEYWORD,
               filters: Optional[SearchFilter] = None,
               limit: int = 50) -> List[SearchResult]:
        """
        Perform search with specified type and filters.
        
        Args:
            query: Search query string
            search_type: Type of search to perform
            filters: Additional filters to apply
            limit: Maximum number of results
            
        Returns:
            List of SearchResult objects sorted by relevance
        """
        if not query.strip():
            return []
        
        # Perform search based on type
        if search_type == SearchType.KEYWORD:
            results = self._keyword_search(query, limit * 2)  # Get more for filtering
        elif search_type == SearchType.MANUFACTURER:
            results = self._manufacturer_search(query, limit * 2)
        elif search_type == SearchType.CATEGORY:
            results = self._category_search(query, limit * 2)
        elif search_type == SearchType.INTERFACE:
            results = self._interface_search(query, limit * 2)
        elif search_type == SearchType.TECHNOLOGY:
            results = self._technology_search(query, limit * 2)
        elif search_type == SearchType.CHIPSET:
            results = self._chipset_search(query, limit * 2)
        elif search_type == SearchType.PRIORITY:
            results = self._priority_search(query, limit * 2)
        else:  # COMBINED
            results = self._combined_search(query, limit * 2)
        
        # Apply filters
        if filters:
            results = self._apply_filters(results, filters)
        
        # Sort by relevance and limit results
        results.sort()
        return results[:limit]
    
    def _keyword_search(self, query: str, limit: int) -> List[SearchResult]:
        """Perform keyword-based search."""
        # Use database FTS search
        resource_defs = self.db_manager.search_by_keywords(query, limit)
        
        # Convert to SearchResult with relevance scoring
        results = []
        for resdef in resource_defs:
            score, matched_fields = self._calculate_relevance_score(resdef, query)
            results.append(SearchResult(
                resource_definition=resdef,
                relevance_score=score,
                matched_fields=matched_fields
            ))
        
        return results
    
    def _manufacturer_search(self, query: str, limit: int) -> List[SearchResult]:
        """Search by manufacturer."""
        resource_defs = self.db_manager.search_by_manufacturer(query, limit)
        
        results = []
        for resdef in resource_defs:
            score = 1.0  # Base score for manufacturer match
            if resdef.manufacturer and query.lower() in resdef.manufacturer.lower():
                if resdef.manufacturer.lower() == query.lower():
                    score = 2.0  # Exact match gets higher score
                
            results.append(SearchResult(
                resource_definition=resdef,
                relevance_score=score,
                matched_fields=['manufacturer']
            ))
        
        return results
    
    def _category_search(self, query: str, limit: int) -> List[SearchResult]:
        """Search by category."""
        resource_defs = self.db_manager.search_by_category(query, limit)
        
        results = []
        for resdef in resource_defs:
            results.append(SearchResult(
                resource_definition=resdef,
                relevance_score=1.0,
                matched_fields=['category']
            ))
        
        return results
    
    def _interface_search(self, query: str, limit: int) -> List[SearchResult]:
        """Search by interface type."""
        # Get all resource definitions and filter by interface
        all_resdefs = self.db_manager.search_by_keywords("", limit * 5)  # Get more for filtering
        
        results = []
        query_lower = query.lower()
        
        for resdef in all_resdefs:
            if resdef.interface and query_lower in resdef.interface.lower():
                score = 1.0
                if resdef.interface.lower() == query_lower:
                    score = 2.0  # Exact match
                
                results.append(SearchResult(
                    resource_definition=resdef,
                    relevance_score=score,
                    matched_fields=['interface']
                ))
        
        return results[:limit]
    
    def _technology_search(self, query: str, limit: int) -> List[SearchResult]:
        """Search by technology (from braces in title)."""
        # Get all resource definitions and filter by technologies
        all_resdefs = self.db_manager.search_by_keywords("", limit * 5)
        
        results = []
        query_lower = query.lower()
        
        for resdef in all_resdefs:
            if resdef.technologies and query_lower in resdef.technologies.lower():
                results.append(SearchResult(
                    resource_definition=resdef,
                    relevance_score=1.0,
                    matched_fields=['technologies']
                ))
        
        return results[:limit]
    
    def _chipset_search(self, query: str, limit: int) -> List[SearchResult]:
        """Search by chipset (from brackets in title)."""
        # Get all resource definitions and filter by chipsets
        all_resdefs = self.db_manager.search_by_keywords("", limit * 5)
        
        results = []
        query_lower = query.lower()
        
        for resdef in all_resdefs:
            if resdef.chipsets and query_lower in resdef.chipsets.lower():
                results.append(SearchResult(
                    resource_definition=resdef,
                    relevance_score=1.0,
                    matched_fields=['chipsets']
                ))
        
        return results[:limit]
    
    def _priority_search(self, query: str, limit: int) -> List[SearchResult]:
        """Search by priority level."""
        try:
            priority = int(query)
        except ValueError:
            return []
        
        # Get all resource definitions and filter by priority
        all_resdefs = self.db_manager.search_by_keywords("", limit * 5)
        
        results = []
        for resdef in all_resdefs:
            if resdef.priority == priority:
                results.append(SearchResult(
                    resource_definition=resdef,
                    relevance_score=1.0,
                    matched_fields=['priority']
                ))
        
        return results[:limit]
    
    def _combined_search(self, query: str, limit: int) -> List[SearchResult]:
        """Perform combined search across multiple fields."""
        # Start with keyword search as base
        keyword_results = self._keyword_search(query, limit)
        
        # Add manufacturer search results
        manufacturer_results = self._manufacturer_search(query, limit // 2)
        
        # Combine and deduplicate results
        combined_results = {}
        
        # Add keyword results
        for result in keyword_results:
            resdef_id = result.resource_definition.resdef_id
            combined_results[resdef_id] = result
        
        # Add manufacturer results (merge if already exists)
        for result in manufacturer_results:
            resdef_id = result.resource_definition.resdef_id
            if resdef_id in combined_results:
                # Boost score for multiple matches
                existing = combined_results[resdef_id]
                existing.relevance_score += result.relevance_score * 0.5
                existing.matched_fields.extend(result.matched_fields)
            else:
                combined_results[resdef_id] = result
        
        return list(combined_results.values())
    
    def _calculate_relevance_score(self, resdef: ResourceDefinition, query: str) -> Tuple[float, List[str]]:
        """Calculate relevance score for a resource definition."""
        score = 0.0
        matched_fields = []
        query_terms = query.lower().split()
        
        # Score based on different fields
        field_weights = {
            'title': 1.0,
            'manufacturer': 0.8,
            'product_name': 0.8,
            'model_number': 0.6,
            'keywords': 0.4,
            'interface': 0.3,
            'category': 0.2
        }
        
        for field, weight in field_weights.items():
            field_value = getattr(resdef, field, None)
            if field_value:
                field_lower = field_value.lower()
                field_score = 0.0
                
                for term in query_terms:
                    if term in field_lower:
                        # Exact word match gets higher score
                        if f" {term} " in f" {field_lower} ":
                            field_score += 1.0
                        else:
                            field_score += 0.5
                
                if field_score > 0:
                    score += field_score * weight
                    matched_fields.append(field)
        
        return score, matched_fields
    
    def _apply_filters(self, results: List[SearchResult], filters: SearchFilter) -> List[SearchResult]:
        """Apply filters to search results."""
        filtered_results = []
        
        for result in results:
            resdef = result.resource_definition
            
            # Apply manufacturer filter
            if filters.manufacturer:
                if not resdef.manufacturer or filters.manufacturer.lower() not in resdef.manufacturer.lower():
                    continue
            
            # Apply category filter
            if filters.category:
                if resdef.category != filters.category:
                    continue
            
            # Apply interface filter
            if filters.interface:
                if not resdef.interface or filters.interface.lower() not in resdef.interface.lower():
                    continue
            
            # Apply priority range filter
            if filters.priority_min is not None:
                if not resdef.priority or resdef.priority < filters.priority_min:
                    continue
            
            if filters.priority_max is not None:
                if not resdef.priority or resdef.priority > filters.priority_max:
                    continue
            
            # Apply store SKU filter
            if filters.has_store_sku is not None:
                has_sku = bool(resdef.store_sku)
                if has_sku != filters.has_store_sku:
                    continue
            
            filtered_results.append(result)
        
        return filtered_results
    
    def suggest_search_terms(self, partial_query: str, limit: int = 10) -> List[str]:
        """Suggest search terms based on partial query."""
        suggestions = set()
        partial_lower = partial_query.lower()
        
        if len(partial_lower) < 2:
            return []
        
        # Get manufacturers
        manufacturers = self.db_manager.get_all_manufacturers()
        for manufacturer in manufacturers:
            if manufacturer and partial_lower in manufacturer.lower():
                suggestions.add(manufacturer)
        
        # Get categories
        categories = self.db_manager.get_all_categories()
        for category in categories:
            if category and partial_lower in category.lower():
                suggestions.add(category.replace('_', ' ').title())
        
        # Limit and sort suggestions
        sorted_suggestions = sorted(list(suggestions))
        return sorted_suggestions[:limit]
    
    def get_search_statistics(self) -> Dict[str, Any]:
        """Get search-related statistics."""
        total_count = self.db_manager.get_count()
        manufacturers = self.db_manager.get_all_manufacturers()
        categories = self.db_manager.get_all_categories()
        
        return {
            'total_resource_definitions': total_count,
            'unique_manufacturers': len(manufacturers),
            'unique_categories': len(categories),
            'manufacturers': manufacturers,
            'categories': categories
        }
