"""
Database models for Resource Definition local storage.

This module defines the database schema and models for storing resource definitions
locally to enable keyword-based search functionality.
"""

import sqlite3
import re
from dataclasses import dataclass
from typing import Optional, List, Dict, Any, Tuple
from pathlib import Path
from config import DatabaseConfig


@dataclass
class ResourceDefinition:
    """Data model for resource definition."""
    resdef_id: str
    title: str
    priority: Optional[int] = None
    
    # Parsed components from title
    store_sku: Optional[str] = None
    manufacturer: Optional[str] = None
    product_name: Optional[str] = None
    model_number: Optional[str] = None
    chipsets: Optional[str] = None  # JSON string of list
    technologies: Optional[str] = None  # JSON string of list
    comments: Optional[str] = None
    interface: Optional[str] = None
    
    # Searchable fields
    keywords: Optional[str] = None  # Space-separated keywords
    device_attributes: Optional[str] = None  # JSON string of list
    category: Optional[str] = None
    
    # Metadata
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database operations."""
        return {
            'resdef_id': self.resdef_id,
            'title': self.title,
            'priority': self.priority,
            'store_sku': self.store_sku,
            'manufacturer': self.manufacturer,
            'product_name': self.product_name,
            'model_number': self.model_number,
            'chipsets': self.chipsets,
            'technologies': self.technologies,
            'comments': self.comments,
            'interface': self.interface,
            'keywords': self.keywords,
            'device_attributes': self.device_attributes,
            'category': self.category,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ResourceDefinition':
        """Create instance from dictionary."""
        return cls(**data)


class DatabaseSchema:
    """Database schema definitions and creation."""
    
    # Main table schema
    CREATE_RESOURCE_DEFINITIONS_TABLE = """
    CREATE TABLE IF NOT EXISTS resource_definitions (
        resdef_id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        priority INTEGER,
        store_sku TEXT,
        manufacturer TEXT,
        product_name TEXT,
        model_number TEXT,
        chipsets TEXT,  -- JSON array
        technologies TEXT,  -- JSON array
        comments TEXT,
        interface TEXT,
        keywords TEXT,  -- Space-separated for search
        device_attributes TEXT,  -- JSON array
        category TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    # Full-text search table schema
    CREATE_FTS_TABLE = """
    CREATE VIRTUAL TABLE IF NOT EXISTS resource_definitions_fts USING fts5(
        resdef_id,
        title,
        manufacturer,
        product_name,
        model_number,
        keywords,
        category,
        content='resource_definitions',
        content_rowid='rowid'
    );
    """
    
    # Indexes for better performance
    CREATE_INDEXES = [
        "CREATE INDEX IF NOT EXISTS idx_manufacturer ON resource_definitions(manufacturer);",
        "CREATE INDEX IF NOT EXISTS idx_priority ON resource_definitions(priority);",
        "CREATE INDEX IF NOT EXISTS idx_category ON resource_definitions(category);",
        "CREATE INDEX IF NOT EXISTS idx_interface ON resource_definitions(interface);",
    ]
    
    # Triggers to keep FTS table in sync
    CREATE_FTS_TRIGGERS = [
        """
        CREATE TRIGGER IF NOT EXISTS resource_definitions_ai AFTER INSERT ON resource_definitions BEGIN
            INSERT INTO resource_definitions_fts(resdef_id, title, manufacturer, product_name, model_number, keywords, category)
            VALUES (new.resdef_id, new.title, new.manufacturer, new.product_name, new.model_number, new.keywords, new.category);
        END;
        """,
        """
        CREATE TRIGGER IF NOT EXISTS resource_definitions_ad AFTER DELETE ON resource_definitions BEGIN
            INSERT INTO resource_definitions_fts(resource_definitions_fts, resdef_id, title, manufacturer, product_name, model_number, keywords, category)
            VALUES ('delete', old.resdef_id, old.title, old.manufacturer, old.product_name, old.model_number, old.keywords, old.category);
        END;
        """,
        """
        CREATE TRIGGER IF NOT EXISTS resource_definitions_au AFTER UPDATE ON resource_definitions BEGIN
            INSERT INTO resource_definitions_fts(resource_definitions_fts, resdef_id, title, manufacturer, product_name, model_number, keywords, category)
            VALUES ('delete', old.resdef_id, old.title, old.manufacturer, old.product_name, old.model_number, old.keywords, old.category);
            INSERT INTO resource_definitions_fts(resdef_id, title, manufacturer, product_name, model_number, keywords, category)
            VALUES (new.resdef_id, new.title, new.manufacturer, new.product_name, new.model_number, new.keywords, new.category);
        END;
        """
    ]
    
    @classmethod
    def create_all_tables(cls, connection: sqlite3.Connection) -> None:
        """Create all tables, indexes, and triggers."""
        cursor = connection.cursor()
        
        # Create main table
        cursor.execute(cls.CREATE_RESOURCE_DEFINITIONS_TABLE)
        
        # Create FTS table if enabled
        if DatabaseConfig.ENABLE_FTS:
            cursor.execute(cls.CREATE_FTS_TABLE)
            
            # Create FTS triggers
            for trigger in cls.CREATE_FTS_TRIGGERS:
                cursor.execute(trigger)
        
        # Create indexes
        for index in cls.CREATE_INDEXES:
            cursor.execute(index)
        
        connection.commit()


class TitleParser:
    """Parser for resource definition titles according to Resource Definition.md rules."""
    
    # Regex patterns for parsing title components
    STORE_SKU_PATTERN = r'\(Store\s+([A-Z0-9/]+)\)'
    CHIPSETS_PATTERN = r'\[([^\]]+)\]'
    TECHNOLOGIES_PATTERN = r'\{([^}]+)\}'
    COMMENTS_PATTERN = r'<([^>]+)>'
    MODEL_NUMBER_PATTERN = r'\(([^)]+)\)(?!\s*\[|\s*\{|\s*<)'
    INTERFACE_PATTERN = r'-\s*([A-Z0-9_/]+(?:\s*[A-Z0-9_/]+)*)$'
    
    @classmethod
    def parse_title(cls, title: str) -> Dict[str, Any]:
        """Parse a resource definition title into components."""
        result = {
            'store_sku': None,
            'manufacturer': None,
            'product_name': None,
            'model_number': None,
            'chipsets': None,
            'technologies': None,
            'comments': None,
            'interface': None,
            'keywords': None,
            'category': None
        }
        
        # Extract store SKU
        store_match = re.search(cls.STORE_SKU_PATTERN, title)
        if store_match:
            result['store_sku'] = store_match.group(1)
        
        # Extract chipsets
        chipset_matches = re.findall(cls.CHIPSETS_PATTERN, title)
        if chipset_matches:
            result['chipsets'] = str(chipset_matches)  # Store as string representation
        
        # Extract technologies
        tech_matches = re.findall(cls.TECHNOLOGIES_PATTERN, title)
        if tech_matches:
            result['technologies'] = str(tech_matches)  # Store as string representation
        
        # Extract comments
        comment_matches = re.findall(cls.COMMENTS_PATTERN, title)
        if comment_matches:
            result['comments'] = ' '.join(comment_matches)
        
        # Extract interface (after the last hyphen)
        interface_match = re.search(cls.INTERFACE_PATTERN, title)
        if interface_match:
            result['interface'] = interface_match.group(1).strip()
        
        # Extract model number (parentheses not followed by brackets, braces, or angle brackets)
        model_matches = re.findall(r'\(([^)]+)\)', title)
        if model_matches:
            # Filter out store SKU matches
            for match in model_matches:
                if not match.startswith('Store '):
                    result['model_number'] = match
                    break
        
        # Extract manufacturer and product name (simplified approach)
        # Remove all parsed components to get the core product info
        clean_title = title
        for pattern in [cls.STORE_SKU_PATTERN, cls.CHIPSETS_PATTERN, 
                       cls.TECHNOLOGIES_PATTERN, cls.COMMENTS_PATTERN]:
            clean_title = re.sub(pattern, '', clean_title)
        
        # Remove interface part
        if result['interface']:
            clean_title = clean_title.replace(f"- {result['interface']}", '')
            clean_title = clean_title.replace(f"-{result['interface']}", '')
        
        # Split into manufacturer and product name
        parts = clean_title.strip().split(' ', 1)
        if len(parts) >= 2:
            result['manufacturer'] = parts[0]
            result['product_name'] = parts[1].strip()
        elif len(parts) == 1:
            result['manufacturer'] = parts[0]
        
        # Generate keywords for search
        keywords = []
        for key, value in result.items():
            if value and key not in ['chipsets', 'technologies']:  # Skip JSON fields
                if isinstance(value, str):
                    keywords.extend(value.split())
        
        # Add original title words
        keywords.extend(title.split())
        
        # Clean and deduplicate keywords
        clean_keywords = []
        for keyword in keywords:
            # Remove special characters and convert to lowercase
            clean_keyword = re.sub(r'[^\w]', '', keyword.lower())
            if clean_keyword and len(clean_keyword) > 1:
                clean_keywords.append(clean_keyword)
        
        result['keywords'] = ' '.join(set(clean_keywords))
        
        # Determine category based on title content
        title_lower = title.lower()
        if any(term in title_lower for term in ['hub', 'dock']):
            result['category'] = 'hub_dock'
        elif any(term in title_lower for term in ['display', 'monitor']):
            result['category'] = 'display'
        elif any(term in title_lower for term in ['drive', 'storage', 'ssd', 'hdd']):
            result['category'] = 'storage'
        elif any(term in title_lower for term in ['cable', 'adapter']):
            result['category'] = 'cable_adapter'
        else:
            result['category'] = 'other'
        
        return result
