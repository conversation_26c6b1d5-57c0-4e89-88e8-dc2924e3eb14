"""
Database Initialization Utility.

This module provides utilities for initializing the local resource definition database,
including setup, migration, and data import functionality.
"""

import logging
from pathlib import Path
from typing import Optional, Tuple

from config import DatabaseConfig
from core.resource_definition_db import ResourceDefinitionDB
from core.csv_importer import CSVImporter, ImportResult


class DatabaseInitializer:
    """Utility class for database initialization and management."""
    
    def __init__(self):
        """Initialize database initializer."""
        self.logger = logging.getLogger(__name__)
        self.db_manager = None
        self.csv_importer = None
    
    def initialize_database(self, force_recreate: bool = False) -> bool:
        """
        Initialize the local resource definition database.
        
        Args:
            force_recreate: If True, recreate the database even if it exists
            
        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            db_path = DatabaseConfig.get_database_path()
            
            # Remove existing database if force_recreate is True
            if force_recreate and db_path.exists():
                db_path.unlink()
                self.logger.info(f"Removed existing database: {db_path}")
            
            # Initialize database manager (this will create tables if they don't exist)
            self.db_manager = ResourceDefinitionDB(db_path)
            self.csv_importer = CSVImporter(self.db_manager)
            
            # Check if database is empty and needs sample data
            count = self.db_manager.get_count()
            if count == 0:
                self.logger.info("Database is empty, importing sample data...")
                result = self.csv_importer.import_sample_data()
                if result.successful_imports > 0:
                    self.logger.info(f"Imported {result.successful_imports} sample resource definitions")
                else:
                    self.logger.warning("Failed to import sample data")
            else:
                self.logger.info(f"Database already contains {count} resource definitions")
            
            self.logger.info("Database initialization completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing database: {e}")
            return False
    
    def import_csv_data(self, csv_path: Path, 
                       resdef_id_column: str = 'resourcedefitionID',
                       title_column: str = 'Title', 
                       priority_column: str = 'Priority') -> ImportResult:
        """
        Import resource definitions from CSV file.
        
        Args:
            csv_path: Path to CSV file
            resdef_id_column: Name of column containing resource definition IDs
            title_column: Name of column containing titles
            priority_column: Name of column containing priorities
            
        Returns:
            ImportResult with statistics and errors
        """
        if not self.csv_importer:
            self.db_manager = ResourceDefinitionDB()
            self.csv_importer = CSVImporter(self.db_manager)
        
        self.logger.info(f"Starting CSV import from: {csv_path}")
        
        # Validate CSV format first
        is_valid, errors = self.csv_importer.validate_csv_format(
            csv_path, resdef_id_column, title_column, priority_column
        )
        
        if not is_valid:
            self.logger.error(f"CSV validation failed: {errors}")
            return ImportResult(0, 0, 0, errors)
        
        # Perform import
        result = self.csv_importer.import_from_csv(
            csv_path, resdef_id_column, title_column, priority_column
        )
        
        # Log results
        self.logger.info(f"CSV import completed: {result.successful_imports}/{result.total_rows} successful")
        if result.errors:
            self.logger.warning(f"Import had {len(result.errors)} errors")
            for error in result.errors[:5]:  # Log first 5 errors
                self.logger.warning(f"Import error: {error}")
        
        return result
    
    def validate_csv_file(self, csv_path: Path, 
                         resdef_id_column: str = 'resourcedefitionID',
                         title_column: str = 'Title', 
                         priority_column: str = 'Priority') -> Tuple[bool, list]:
        """
        Validate CSV file format without importing.
        
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        if not self.csv_importer:
            self.csv_importer = CSVImporter()
        
        return self.csv_importer.validate_csv_format(
            csv_path, resdef_id_column, title_column, priority_column
        )
    
    def get_database_info(self) -> dict:
        """Get information about the current database."""
        if not self.db_manager:
            self.db_manager = ResourceDefinitionDB()
        
        try:
            db_path = DatabaseConfig.get_database_path()
            count = self.db_manager.get_count()
            manufacturers = self.db_manager.get_all_manufacturers()
            categories = self.db_manager.get_all_categories()
            
            return {
                'database_path': str(db_path),
                'database_exists': db_path.exists(),
                'database_size_bytes': db_path.stat().st_size if db_path.exists() else 0,
                'total_resource_definitions': count,
                'unique_manufacturers': len(manufacturers),
                'unique_categories': len(categories),
                'manufacturers': manufacturers[:10],  # First 10 manufacturers
                'categories': categories
            }
        except Exception as e:
            self.logger.error(f"Error getting database info: {e}")
            return {'error': str(e)}
    
    def clear_database(self) -> bool:
        """Clear all data from the database."""
        if not self.db_manager:
            self.db_manager = ResourceDefinitionDB()
        
        try:
            success = self.db_manager.clear_all()
            if success:
                self.logger.info("Database cleared successfully")
            else:
                self.logger.error("Failed to clear database")
            return success
        except Exception as e:
            self.logger.error(f"Error clearing database: {e}")
            return False
    
    def backup_database(self, backup_path: Optional[Path] = None) -> bool:
        """Create a backup of the database."""
        try:
            db_path = DatabaseConfig.get_database_path()
            if not db_path.exists():
                self.logger.error("Database does not exist, cannot create backup")
                return False
            
            if backup_path is None:
                backup_path = db_path.parent / f"{db_path.stem}_backup{db_path.suffix}"
            
            # Simple file copy for SQLite database
            import shutil
            shutil.copy2(db_path, backup_path)
            
            self.logger.info(f"Database backed up to: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating database backup: {e}")
            return False
    
    def restore_database(self, backup_path: Path) -> bool:
        """Restore database from backup."""
        try:
            if not backup_path.exists():
                self.logger.error(f"Backup file does not exist: {backup_path}")
                return False
            
            db_path = DatabaseConfig.get_database_path()
            
            # Simple file copy for SQLite database
            import shutil
            shutil.copy2(backup_path, db_path)
            
            self.logger.info(f"Database restored from: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error restoring database: {e}")
            return False


# Global instance for easy access
database_initializer = DatabaseInitializer()


def initialize_database_on_startup(force_recreate: bool = False) -> bool:
    """
    Initialize database on application startup.
    
    This function should be called during application initialization.
    """
    return database_initializer.initialize_database(force_recreate)


def import_csv_on_startup(csv_path: Path) -> ImportResult:
    """
    Import CSV data on application startup.
    
    This function can be called to import initial data during startup.
    """
    return database_initializer.import_csv_data(csv_path)
