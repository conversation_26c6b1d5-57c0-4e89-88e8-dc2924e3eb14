# Plan: Building a Professional & Internal Context MCP Server

This plan outlines the strategy for creating a new MCP (Model Context Protocol) server. The goal of this server is to provide professional and internal context to the agent, enabling it to perform tasks that require access to specialized or proprietary information.

## 1. Comparison of Implementation Approaches

There are two primary ways to implement an MCP server. The best choice depends on where the data and services are located.

| Feature | Local (Stdio) Server | Remote (SSE) Server | Recommendation |
| :--- | :--- | :--- | :--- |
| **Hosting** | Runs as a script/executable on the local machine. | Hosted on a remote server, accessible via a URL. | **Local** is simpler for initial development and for accessing local resources. |
| **Access** | Can directly access local files, scripts, and CLIs. | Can access web APIs and databases hosted remotely. | If context is in local files or requires local tools, **Local** is better. If context is from a web service, **Remote** is the only option. |
| **Security** | Credentials and scripts are stored on the user's machine. | Credentials are sent over the network (requires HTTPS). | **Local** is generally more secure for sensitive local data. |
| **Scalability** | Limited by the user's machine resources. | Can be scaled independently on a server. | Not a primary concern for this use case. |

**Conclusion:** We will build a **Local (Stdio) Server** implemented in Python. This approach is the most flexible for accessing local data, scripts, and internal command-line tools.

## 2. High-Level Architecture

The architecture will now include a resource index to support the new search functionality.

```mermaid
graph TD
    subgraph User's Machine
        A[Agent]
        B[MCP Host]
        C[Python MCP Server]
    end

    subgraph Internal Systems
        D[Internal CLI Tool]
        E[Internal Knowledge Base API]
        F[Local Files/Databases]
        G[Resource Index <br> e.g., JSON file]
    end

    A -- 1. "Find resources about X" --> B
    B -- 2. Calls find_resources tool --> C
    C -- 3. Searches --> G
    C -- 4. Returns list of matching resources --> B
    B -- 5. Presents list to --> A
    A -- 6. "Get resource Y" --> B
    B -- 7. Calls get_resource tool --> C
    C -- 8. Reads from --> F
    C -- 9. Returns resource content --> B
    B -- 10. Presents content to --> A
```

## 3. Development Plan

Here is the updated to-do list for building and deploying the MCP server.

- **Step 1: Setup Python Project:** Create a new directory for the MCP server and set up a Python virtual environment.
- **Step 2: Create a Resource Index:** Define and create a simple index file (e.g., `resources.json`) that maps resource IDs to their descriptions and locations.
- **Step 3: Implement the Core Server:** Write the main Python script for the server.
- **Step 4: Define & Implement Tools:**
    - **`find_resources(query)`:** This new tool will search the `resources.json` index for resources matching the query and return a list of names and descriptions.
    - **`get_resource(resource_id)`:** This tool will use the ID to look up the resource's location in the index and return its full content.
- **Step 5: Handle Configuration & Credentials:** The server will read necessary API keys or paths from environment variables. We will document what users need to set.
- **Step 6: Register the MCP Server:** Add the new server to the `mcp_settings.json` file so the agent can discover and use its tools.
- **Step 7: Test the Server:**
    - First, use `find_resources` to discover available resources.
    - Then, use `get_resource` to fetch the content of a specific resource.

This updated plan directly addresses the need to avoid context window overload by providing a discovery mechanism for resources.
