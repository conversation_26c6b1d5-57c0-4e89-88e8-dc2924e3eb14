{"createdAt": "2015-09-09T05:49:55+0000", "component": {"id": 1073918, "name": "USB-TBT Inventory", "version": "All"}, "lastModifiedAt": "2025-06-24T05:37:44+0000", "createdBy": {"dsid": *********, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "type": "INTERNAL", "phoneNumber": "+81-3-********", "chat": {"slack": {"userId": "WN6AFJELA", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "lastModifiedBy": {"dsid": *********, "email": "mami_ho<PERSON><EMAIL>", "firstName": "<PERSON><PERSON>", "lastName": "Horikawa", "type": "INTERNAL", "phoneNumber": "+81-3-********", "chat": {"slack": {"userId": "WNK5J736H", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "resources": [{"id": 13306, "title": "AOU367552 | Apple Thunderbolt Display (J59)", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641356, "name": "MSQ - Resources", "version": "Cork"}, "keywords": null, "include": true}, {"id": 13307, "title": "AOU367551 | Apple Thunderbolt Display (J59)", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641356, "name": "MSQ - Resources", "version": "Cork"}, "keywords": null, "include": true}, {"id": 13308, "title": "AOU367550 | Apple Thunderbolt Display (J59)", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641356, "name": "MSQ - Resources", "version": "Cork"}, "keywords": [{"id": 1577866, "name": "Cork Audit 2022"}], "include": true}, {"id": 13469, "title": "AOU308453 | Apple Thunderbolt Display J59 [Pericom 0x03]", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641357, "name": "MSQ - Resources", "version": "Tokyo"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 13567, "title": "J59-B117 PRQ", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": false}, {"id": 13556, "title": "PT749049 | J59 B117 PRQ", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641357, "name": "MSQ - Resources", "version": "Tokyo"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 13559, "title": "PT749050 | J59 B117 PRQ", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641357, "name": "MSQ - Resources", "version": "Tokyo"}, "keywords": [{"id": 1174701, "name": "Box in external WH"}, {"id": 544919, "name": "Tester"}, {"id": 982322, "name": "iTrack-Archived"}], "include": true}, {"id": 13563, "title": "PT749026 | PRQ J59 (Apple Thunderbolt Display (27-inch))", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641356, "name": "MSQ - Resources", "version": "Cork"}, "keywords": null, "include": true}, {"id": 13568, "title": "J59-B117 PRQ", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 14415, "title": "J59 PRQ", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 16047, "title": "PT656009 | PVT J59 [Pericom 0x1]", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641357, "name": "MSQ - Resources", "version": "Tokyo"}, "keywords": [{"id": 544919, "name": "Tester"}, {"id": 1174701, "name": "Box in external WH"}, {"id": 974064, "name": "iTrack"}], "include": true}, {"id": 16046, "title": "PT656011 | PVT J59 (S/N SC02G309GDJGR) (WH)", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641357, "name": "MSQ - Resources", "version": "Tokyo"}, "keywords": [{"id": 544919, "name": "Tester"}, {"id": 682772, "name": "MSQ Peripherals"}, {"id": 974064, "name": "iTrack"}], "include": true}, {"id": 16042, "title": "PT656016 | PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 1056109, "name": "MSQ - Resources - Inactive", "version": "Cork"}, "keywords": null, "include": true}, {"id": 16043, "title": "PT656015 | PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 1056109, "name": "MSQ - Resources - Inactive", "version": "Cork"}, "keywords": [{"id": 1577866, "name": "Cork Audit 2022"}], "include": true}, {"id": 14947, "title": "PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 14948, "title": "PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 15732, "title": "PT643044 | Pre-PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 1056109, "name": "MSQ - Resources - Inactive", "version": "Cork"}, "keywords": null, "include": true}, {"id": 15733, "title": "PT643133 | Pre-PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 1056109, "name": "MSQ - Resources - Inactive", "version": "Cork"}, "keywords": [{"id": 1577866, "name": "Cork Audit 2022"}], "include": true}, {"id": 15833, "title": "Pre-PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 15834, "title": "Pre-PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 16659, "title": "PT867926 | J59 PRQ Thunderbolt [Pericom 0x3] (Keep)", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641357, "name": "MSQ - Resources", "version": "Tokyo"}, "keywords": [{"id": 1007058, "name": "Migration Complete FY18Q3"}, {"id": 544919, "name": "Tester"}, {"id": 982322, "name": "iTrack-Archived"}], "include": true}, {"id": 16870, "title": "J59-B117 PRQ", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 16969, "title": "J59 PRQ", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 17066, "title": "Pre-PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 17151, "title": "Pre-PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 18343, "title": "Pre-PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 19410, "title": "PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 18338, "title": "J59 Pre-PVT Unit #146", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 18340, "title": "Pre-PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 18341, "title": "Pre-PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 18342, "title": "Pre-PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 18225, "title": "Apple Thunderbolt Display ( J59 )", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 18227, "title": "Apple Thunderbolt Display ( J59 )", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 19304, "title": "Apple J59 <Maturity>", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 19305, "title": "Apple J59 <Maturity>", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 19306, "title": "Apple J59 <Maturity>", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 123169, "title": "Apple thunderbolt display(J59)", "createdBy": {"dsid": **********, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "EXTERNAL", "phoneNumber": "0", "chat": null, "systemAccountFlag": false}, "component": {"id": 674435, "name": "MSQ - Resources", "version": "WGT"}, "keywords": null, "include": true}, {"id": 19662, "title": "PT643178 | Pre-PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 1056109, "name": "MSQ - Resources - Inactive", "version": "Cork"}, "keywords": null, "include": true}, {"id": 19661, "title": "PT643181 | J59 Pre-PVT", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641356, "name": "MSQ - Resources", "version": "Cork"}, "keywords": null, "include": true}, {"id": 19605, "title": "AOU308454 | Apple Thunderbolt Display (J59) (Keep) [Pericom 0x1]", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641357, "name": "MSQ - Resources", "version": "Tokyo"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 19783, "title": "J59-B117 PRQ", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 19984, "title": "PT670787 | PRQ J59 [PRQ1-ErP Compliance][Pericom 0x03] (Keep)(<PERSON><PERSON>'s test site)", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641357, "name": "MSQ - Resources", "version": "Tokyo"}, "keywords": [{"id": 1007058, "name": "Migration Complete FY18Q3"}, {"id": 982322, "name": "iTrack-Archived"}, {"id": 544919, "name": "Tester"}], "include": true}, {"id": 20034, "title": "PT656010 | PVT J59 [Pericom 0x01]", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641357, "name": "MSQ - Resources", "version": "Tokyo"}, "keywords": [{"id": 544919, "name": "Tester"}, {"id": 1174701, "name": "Box in external WH"}, {"id": 974064, "name": "iTrack"}], "include": true}, {"id": 68300, "title": "PT656014 J59 PVT", "createdBy": {"dsid": 109755, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "type": "INTERNAL", "phoneNumber": "******-9741225", "chat": {"slack": {"userId": "WNLV7Q9P1", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 660363, "name": "MSQ - Resources", "version": "Cupertino - Software"}, "keywords": null, "include": true}, {"id": 123168, "title": "Apple thunderbolt display(J59)", "createdBy": {"dsid": **********, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "EXTERNAL", "phoneNumber": "0", "chat": null, "systemAccountFlag": false}, "component": {"id": 674435, "name": "MSQ - Resources", "version": "WGT"}, "keywords": null, "include": true}, {"id": 22484, "title": "PT749013 | J59 B117 PRQ", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641357, "name": "MSQ - Resources", "version": "Tokyo"}, "keywords": [{"id": 982322, "name": "iTrack-Archived"}, {"id": 1007058, "name": "Migration Complete FY18Q3"}, {"id": 544919, "name": "Tester"}, {"id": 1174701, "name": "Box in external WH"}], "include": true}, {"id": 22730, "title": "AOU367554 | Apple Thunderbolt Display (J59)", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 1056109, "name": "MSQ - Resources - Inactive", "version": "Cork"}, "keywords": null, "include": true}, {"id": 22762, "title": "PT656017 | PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 1056109, "name": "MSQ - Resources - Inactive", "version": "Cork"}, "keywords": null, "include": true}, {"id": 23027, "title": "J59 PRQ", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 23222, "title": "J59 PRQ6", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 23467, "title": "Apple J59 <Maturity>", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 23468, "title": "Apple J59 <Maturity>", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 23541, "title": "J59 Pre-PVT", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 23602, "title": "Pre-PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 23604, "title": "Pre-PVT J59", "createdBy": {"dsid": 3723, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "NO_ACCESS", "phoneNumber": "******-8624102", "chat": {"slack": {"userId": "WNK3UHEHE", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": [{"id": 544919, "name": "Tester"}], "include": true}, {"id": 24643, "title": "J59 Thunderbolt Cinema Display", "createdBy": {"dsid": 11207, "email": "<EMAIL>", "firstName": "Don", "lastName": "<PERSON>", "type": "INTERNAL", "phoneNumber": "+353-21-4284804", "chat": {"slack": {"userId": "WNCLZQWLR", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 1056109, "name": "MSQ - Resources - Inactive", "version": "Cork"}, "keywords": [{"id": 539303, "name": "WGT: Legacy Graphics"}], "include": true}, {"id": 27738, "title": "AOU308053 | Apple Thunderbolt display (J59 Production)[Pericom 0x01] (Keep) *EAM18617 ", "createdBy": {"dsid": 67615, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "INTERNAL", "phoneNumber": "******-9748159", "chat": {"slack": {"userId": "WNCLZVCF3", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641357, "name": "MSQ - Resources", "version": "Tokyo"}, "keywords": [{"id": 1007058, "name": "Migration Complete FY18Q3"}, {"id": 458010, "name": "Tester"}], "include": true}, {"id": 27742, "title": "AOU308058 | Apple Thunderbolt display (J59) [MC914LL/A] (Keep) *EAM18616", "createdBy": {"dsid": 67615, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "INTERNAL", "phoneNumber": "******-9748159", "chat": {"slack": {"userId": "WNCLZVCF3", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641357, "name": "MSQ - Resources", "version": "Tokyo"}, "keywords": [{"id": 1007058, "name": "Migration Complete FY18Q3"}, {"id": 458010, "name": "Tester"}], "include": true}, {"id": 27743, "title": "AOU308059 | Apple Thunderbolt display (J59) [MC914LL/A] [Pericom 0x01](Keep) *EAM18631", "createdBy": {"dsid": 67615, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "INTERNAL", "phoneNumber": "******-9748159", "chat": {"slack": {"userId": "WNCLZVCF3", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 641357, "name": "MSQ - Resources", "version": "Tokyo"}, "keywords": [{"id": 1007058, "name": "Migration Complete FY18Q3"}, {"id": 458010, "name": "Tester"}], "include": true}, {"id": 28340, "title": "PT867925 | J59 PRQ Thunderbolt", "createdBy": {"dsid": 9004046, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "type": "INTERNAL", "phoneNumber": "******-9742954", "chat": {"slack": {"userId": "WNK472D4G", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": null, "include": true}, {"id": 28360, "title": "PT867925 | J59 PRQ Thunderbolt", "createdBy": {"dsid": 9004046, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "type": "INTERNAL", "phoneNumber": "******-9742954", "chat": {"slack": {"userId": "WNK472D4G", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 539886, "name": "MSQ - Resources", "version": "All"}, "keywords": null, "include": true}, {"id": 46040, "title": "PT656012 J59-PVT", "createdBy": {"dsid": 109755, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "type": "INTERNAL", "phoneNumber": "******-9741225", "chat": {"slack": {"userId": "WNLV7Q9P1", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 660363, "name": "MSQ - Resources", "version": "Cupertino - Software"}, "keywords": null, "include": true}, {"id": 78806, "title": "Apple Thunderbolt display (J59) Display", "createdBy": {"dsid": **********, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "INTERNAL", "phoneNumber": "******-7839121", "chat": {"slack": {"userId": "WPPSPLH3Q", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 1035303, "name": "CoreTransportsQA", "version": "Resources"}, "keywords": [{"id": 975496, "name": "TBTDevice"}], "include": false}, {"id": 119882, "title": "J59 Thunderbolt Display (J59-B117-PRQ) PT670702", "createdBy": {"dsid": **********, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "NO_ACCESS", "phoneNumber": "0", "chat": {"slack": {"userId": "WPWEKV0RW", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "component": {"id": 1035303, "name": "CoreTransportsQA", "version": "Resources"}, "keywords": null, "include": true}, {"id": 123166, "title": "Apple thunderbolt display(J59)", "createdBy": {"dsid": **********, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "EXTERNAL", "phoneNumber": "0", "chat": null, "systemAccountFlag": false}, "component": {"id": 674435, "name": "MSQ - Resources", "version": "WGT"}, "keywords": null, "include": true}, {"id": 123167, "title": "Apple thunderbolt display(J59)", "createdBy": {"dsid": **********, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "type": "EXTERNAL", "phoneNumber": "0", "chat": null, "systemAccountFlag": false}, "component": {"id": 674435, "name": "MSQ - Resources", "version": "WGT"}, "keywords": null, "include": true}], "id": 10153, "originator": {"dsid": *********, "email": "<EMAIL>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "type": "INTERNAL", "phoneNumber": "+81-3-********", "chat": {"slack": {"userId": "WN6AFJELA", "teamId": "AWDC232SS"}}, "systemAccountFlag": false}, "title": "Apple Thunderbolt Display 27 inch (J59) [LR][Pericom] - TBT_10", "category": {"id": 5, "value": "Display"}, "priority": "1", "resourceClass": {"id": 1, "value": "Hardware"}}